body {
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    overflow-x: hidden;
    overflow-y: hidden;
    background-image: url(../images/dark\ forest\ picture.jpg);
    background-repeat: no-repeat;
    background-size: cover;

}
#modalbutton{
    color:black;
    font-weight: 800px;
}
.container-fluid {
    height: 100vh;   
}
 #playeroness{
    color: aqua;
 }
 #playertwoo{
    color: aqua;
 }
.head {
    width: 100vw;
    height: 10vh;
    background: transparent;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    
}
.scoreboard{
    align-items: center;
}
#playername{
    color:red; /* White text color */
    font-family: 'Vampiro One', cursive;
}
#score{
    color:red; /* White text color */
    font-family: 'Vampiro One', cursive;
}

.gameover{
    
    display: flex;
    justify-content: center;
}
.regform{
    margin-top: 150px;
    height: 40vh;
    width: 40vw;
}
#battle{
    width: 30%;
    height: 80%;
    background-image: url(../images/red.jpg);
    font-weight: 900;
    color: black;
}

.buttonbattle{
    margin-top: 8%;
}
#battle{
    width:20%;
    margin-top:2%;
}
.remover{
    display: none;
}
.riddle{
    margin-top:70px;
}
.riddlecontent{
    height: 80vh;
}
#riddles{
    width: 100%;
    height: 40vh;
    border: 1px solid white;
    font-size: 30px;
    background-image: url(../images/red.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    border-style: ridge;
    border-radius: 10px;  
}
#riddle{
    color: aqua;
}
.batttlee{
    display: flex;
    justify-content: center;
}
input[type="text"]{
    background-image: url(../images/red.jpg);
    background-repeat: no-repeat;
    background-size: cover;
}
#useranswer{
    height: 50px;
    color: aqua;
}

/*!gameover */
.gameoverContainer{
    width:100%;
    height: 45vh;

    
}
#gameover{
    color: red;
    font-size: 60px;
    font-weight: 800;
}
.imager{
    width: 100%;
    height: 25%;
    display: flex;
    justify-content: center;
}
#image{
    width:60px;
    height: 100%;
}
.winner{
    width:100%;
    height: 25%;
    margin-top: 0;
    
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 30px;
    font-weight: bold;
}
#winnerplayer{
    font-size: 30px;
	font-weight: 1000;
    color: transparent; 
    -webkit-text-stroke: 1px white;
    background-image: url(../images/red.jpg);
    -webkit-background-clip: text;
    background-position: 0 0;
    animation: back 20s linear infinite;
    text-align: center;
}
@keyframes back{
    100%{
        background-position: 2000px 0;
    }
}
#introhead{
    font-size: 40px;
	font-weight: 1000;
    color: transparent; 
    -webkit-text-stroke: 1px white;
    background-image: url( ../images/red.jpg);
    -webkit-background-clip: text;
    background-position: 0 0;
    animation: back 20s linear infinite;
    text-align: center;
}
@keyframes back{
    100%{
        background-position: 2000px 0;
    }
}
#submit{
    border-radius: 10px;
    background-image: url(../images/red.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    font-size: 20px;
    font-weight: 600;
    color: black;
}
#back {
    width: 30%;
    height: 100%;
    border-radius: 10px;
    background-image: url(../images/red.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    font-size: 20px;
    font-weight: 600;
    
  }
/* !media query */
@media screen and (max-width: 768px){
    #riddle{
        color: aqua;
        font-size: 18px;
    }
    #introhead{
        font-size:25px;
    }
    #playername{
        font-size: 15px;
    }
    #score{
        font-size: 15px;
    }
    .regform{
        height: 40vh;
        width: 40vw;
    }
    #back{
        font-size: 15px;
        font-weight: 400;
        width: 40%;
    }
}

