var riddle1 = [
  "How many months have 28 days?",
  "What nevers ask questions but is always answered?",
  "I am an odd number,take away a letter I become even.What number am I?",
  "What has hands but cannot clap?",
  "What is easy to get into but hard to get out?",
  "I am tall when I am young but am short when I am old who am I",
  "What is full hole but still holds water",
  "Which is heavier a kilo of stone or a kilo of featther",
  "What is at the end of rainbow",
];

var riddle2 = [
  "What three letter changes a girl to a woman?",
  "What can you easily break but you can never touch it?",
  "what question can you ask where you can get different answers every time but all the answers being correct?",
  "I am first on earth,second in heaven.I appear twice in a week,never in a month,but once ina year.What am I?",
  "What's in the beginning and the end,but not not in the middle?",
  "When can you add 2 to 11 and get 1 as the answer?",
  "Say my name and I am no more?",
  "What can you hold in your right hand but never in your left hand?",
  "What can be larger than you without weighing anything?",
];
var riddle3 = [
  "What do <PERSON> the Eighth and <PERSON>rm<PERSON> the <PERSON> have in common?",
  "What is the only word that is spelled wrong in the dictionary",
  "I am in life but not in death.You can't have fun without me.What am I?",
  "What does everyone know how to open but not how to close?",
  "I start with E end with with E.I have whole countries inside me.What am I?",
  "I never speak unless spoken to,many have heard me but none have seen me.What am I?",
  "What can fill an entire room without taking up any space?",
];
var riddle4 = [
  "I speak without a mouth and hears without ears.I have no body,but I come alive with wind.What am I?",
  "You measure my life in hours and I serve you by expiring.I am quick when I am thin and slow when I am fat.The wind is my enemy",
  "Three different doctors said that Paul is their brother yet Paul calims he has no brothers.Who is lying",
  "How many letters are in the alphabet?",
  "What gets wet when drying?",
  "I have cities,but no houses.I have mountains,but no trees.I have water,but no fish.What am I?",
  "you see a boat filled with people.It has not sunk,but when you look again you don't see a single person on the boat.Why?",
  "Mr.Taylor has four daoughters and each has a brother.In total,how many children does Mr.Taylor have?",
];
/////////////////////////////////////////////////////////////////////z
var riddle5 = [
  "What can run but never walk,have a mouth but never talk,have a head that never weeps,and a bed that nevers sleeps?",
  "If an electric train is moving north at 55mph and the winds blowing eastt at 70mph,which way does the smoke blow?",
  "There are 6 sisters.Each sister has 1 brother.How many brothers are in the sister's family?",
  "Two fathers and two sons come home from the mall.Yet when they arrive hoeme,only three people get out of the car.How is this possible?",
  "A murder is condemned to death and he has the option to die in one of the the following three rooms:A room full of raging fire,a room full of assassins with loaded guns and a room full of lions who haven't eaten in years.Which room should he choose?",
];
var riddle6 = [
  "If you eat me,my sender will eat you.What am I?",
  "Steve was murdered on saturday afternoon.His wife said she was reading.The doorman said he was in the shower.The chef said he was making breakfast.The gardener was pruning hedges.From the information given,who committed the murder?",
  "If a plane came crashing down on the border between Canada and America,where are the survivors buried?",
];
var riddle7 = [
  "The more these are taken,the more they are left behind.what are they?",
  "I eat to live and drink to die.What could I be?",
  "What is caught but never thrown?",
  "Humans purchase me to eat but then never eat me.what am I?",
  "What is more useful when it is broken?",
  "I am easy to lift, but hard to throw. What am I?",
  " What goes up, but never comes down?",
  "A bus driver goes the wrong way down a one-way street. He passes the cops, but they do not stop him. Why?",
];
var riddle8 = [
  "I am a five-letter word and people eat me.If you remove the first letter I  become an energy form.If you remove the first two letters,I am needed to live.Scramble the lat three letters an and I am a drink.What word am I?",
  "What do we call a woman who always know her husband whereabouts all the time.",
  'A Woman is sittin in her hotel room when someone knocks at the door.she opened the door to find a man she\'s never seen before.He says,"Oh I am sorry,I made a mistake and thought this was my room".He then ran awy and got into an elevator so the woman shut her door and phoned security.What made the woman so suspicious?',
];
var riddle9 = [
  "Rachel goes to the supermarket and buys 10 tomatoes. Unfortunately, on the way back home, all but 9 get ruined. How many tomatoes are left in a good condition?",
  "If you multiply this number by any other number, the answer will always be the same. What number is this?",
  "Two girls were born to the same mother, on the same day, at the same time, in the same month and year, and yet they're not twins. How can this be?",
  "I move very slowly at an imperceptible rate, although I take my time, I am never late. I accompany life and survive past demise; I am viewed with esteem in many women's eyes. What am I?",
];
var riddle10 = [
  "Who has married many women but was never married?",
  "What does a man do only once in his lifetime, but women do once a year after they are 29?",
  "Poor people have it. Rich people do not need it. If you eat it you die. What is it?",
  "They come out at night without being called, and are lost in the day without being stolen. What are they?",
  "What is always in front of you, but cannot be seen?",
  "What goes through cities and fields, but never moves?",
  "What 2 things can you never eat for breakfast?",
  "What is always on its way but never arrives?",
  "What word in the English language fulfils all the following criteria: The first two letters signify a male, the first three letters signify a female, the first four letters signify a great person, while the entire word signifies a great woman. ",
];

var riddle11 = [
  "What English word retains the same pronunciation, even after you take away four of its five letters?",
  "When John was six years old he hammered a nail into his favorite tree to mark his height. Ten years later at age sixteen, John returned to see how much higher the nail was. If the tree grew by five centimeters each year, how much higher would the nail be?",
  "I am four times as old as my daughter. In 20 years time I shall be twice as old as her. How old are we now?",
  " Lily is a lilypad in a small pond. Lilly doubles her size each day, On the 20th day she covers the whole pond. On what day was Lily half the size of the pond?",
  "I am something people love or hate. I change people  appearances and thoughts. If a person takes care of themself, I will go up even higher. To some people, I will fool them. To others, I am a mystery. Some people might want to try and hide me, but I will show. No matter how hard people try, I will never go down. What am I?",
  "If you have me, you want to share me. If you share me, you don't have me.",
];

var riddle12 = [
  " How can you drop a raw egg from a height onto a concrete floor without cracking it?",
  "What tastes better than it smells?",
  "What building has the most stories?",
  " What kind of tree can you carry in your hand?",
  "What starts with T, ends with T, and has T in it?",
  " Where is the only place where today comes before yesterday?",
  "What comes at the end of everything?",
  ' What do the letter "t" and an island have in common?',
  " What kind of ship has two mates but no captain?",
];

var riddle13 = [
  " If you throw a blue stone into the Red Sea, what will it become?",
  "What gets shorter as it grows older?",
  "What five-letter word becomes shorter when you add two letters to it?",
  "What is always found on the ground but never gets dirty?",
  "What has a head and a tail but no body?",
  "What has many teeth but cannot bite?",
  "What has one head, one foot, and four legs?",
  "What gets smaller every time it takes a bath?",
  "What 5-letter word typed in all capital letters can be read the same upside down?",
  "What do you bury when it's alive and dig up when it's dead?",
];

var riddle14 = [
  "What is it that we burry and it comes back to life?",
  'When I say,"You are staggering my son,"which animals am I talking about?',
  " What is taken before you can get it?",
  "What belongs to you but is used by everyone you meet?",
  "What is lighter than a feather but impossible to hold for much more than a minute?",
  " A cowboy rides into town on Friday. He stays three days, then rides out of town on Friday. How?",
  "If your uncle's sister is not your aunt, then who is she to you?",
  " Two people are born at the same moment, but they don't have the same birthdays. How?",
];
var riddle15 = [
  " The person who made it does not need it. The person who bought it does not want it. The person who needs it does not know it. What is it?",
  "A man goes outside in the rain without an umbrella or hat but doesn't get a single hair on his head wet. How?",
  "If you are running a race and pass the person in second, then what place are you in?",
  "You enter a room that contains a match, kerosene lamp, candle, and fireplace. What should you light first?",
  " A mother and father have four daughters, and each daughter has one brother. How many people are in the family?",
  " If the day before yesterday was the 23rd, then what will be the day after tomorrow?",
  "What common English verb becomes its own past tense by rearranging its letters?",
  "A is B's father but B isn't A's son. How?",
  "What is one thing that all people, regardless of their politics or religion, have to agree is between heaven and earth?",
];
var riddles = [
  ...riddle1,
  ...riddle2,
  ...riddle3,
  ...riddle4,
  ...riddle5,
  ...riddle6,
  ...riddle7,
  ...riddle8,
  ...riddle9,
  ...riddle10,
  ...riddle11,
  ...riddle12,
  ...riddle13,
  ...riddle14,
  ...riddle15,
];

//arrays for answers
var answer1 = [
  /all|every month|all months/gi,
  /doorbell/gi,
  /seven|7/gi,
  /clock/gi,
  /trouble/gi,
  /candle/gi,
  /sponge/gi,
  /none|all weigh the same/gi,
  /^w{1}$/gi,
];
var answer2 = [
  /age/gi,
  /promise/gi,
  /what time is it|what is the clock|time/gi,
  /^e{1}$|letter e/gi,
  /^n{1}$|letter n/gi,
  /when you add 2 hours to 11 o'clock|in a clock|time/gi,
  /silence/gi,
  /my lefthand|left hand|your lefthand|lefthand/gi,
  /shadow/gi,
];
var answer3 = [
  /middle name/gi,
  /wrong/gi,
  /letter f|^f{1}$/gi,
  /egg/gi,
  /Europe/gi,
  /Echo/gi,
  /light/gi,
];
var answer4 = [
  /echo/gi,
  /candle/gi,
  /no one|none/gi,
  /eleven|11/gi,
  /towel/gi,
  /map/gi,
  /marrried|couples/gi,
  /five|5/gi,
];
var answer5 = [
  /river/gi,
  /don,t smoke|no smoke/gi,
  /one|1/gi,
  /grandfather,father and son|grandfather,son,father|grandfather,father and grandson/gi,
  /a room full of lions|lions/gi,
];
var answer6 = [/fishhook/gi, /chef/gi, /survivors are not buried|neither/gi];
var answer7 = [
  /footsteps|footsteps/gi,
  /fire/gi,
  /a cold|cold/gi,
  /plate|spoon/gi,
  /egg/gi,
  /feather|feathers/gi,
  /age/gi,
  /walking|on foot/gi,
];
var answer8 = [/wheat/gi, /widow/gi, /don't knock|never knock/gi];
var answer9 = [/nine|9/gi, /zero|0/gi, /triplet/gi, /hair/gi];
var answer10 = [
  /priest/gi,
  /turn 30/gi,
  /nothing/gi,
  /stars|star/gi,
  /future/gi,
  /road|roads/gi,
  /lunch|dinner|supper/gi,
  /tomorrow/gi,
  /heroine/gi,
];
var answer11 = [
  /queue/gi,
  /same|did not change/gi,
  /40|10/gi,
  /19|nineteen/gi,
  /age/gi,
  /secret/gi,
];
var answer12 = [
  /hard|concrete floor are hard to crack/gi,
  /tongue/gi,
  /library/gi,
  /palm/gi,
  /teapot/gi,
  /dictionary/gi,
  /letter g|^g{1}$/gi,
  /middle of water/gi,
  /relationship/gi,
];
var answer13 = [
  /wet/gi,
  /candle/gi,
  /short/gi,
  /shadow/gi,
  /coin/gi,
  /comb/gi,
  /bed/gi,
  /soap/gi,
  /swims/gi,
  /plant/gi,
];
var answer14 = [
  /seed/gi,
  /chameleon/gi,
  /picture/gi,
  /name/gi,
  /breathe/gi,
  /named friday|called friday|horse named friday/gi,
  /mother/gi,
  /timezone/gi,
];
var answer15 = [
  /coffin/gi,
  /bald/gi,
  /second|2/gi,
  /match/gi,
  /seven|7/gi,
  /27th|27/gi,
  /eat/gi,
  /daughter/gi,
  /and/gi,
];
var answers = [
  ...answer1,
  ...answer2,
  ...answer3,
  ...answer4,
  ...answer5,
  ...answer6,
  ...answer7,
  ...answer8,
  ...answer9,
  ...answer10,
  ...answer11,
  ...answer12,
  ...answer13,
  ...answer14,
  ...answer15,
];

//!END OF RIDDLE ARRAYS AND ANSWER ARRAYS
  
  /*******
   * *GETTING ELEMENTS FROM DOM
   */
  var battlebutton = document.getElementById("battle"); //!BUTTON
  var containerr = document.getElementById("regform"); //!BOX CONTAINING PLAYER REGISTRATION NAMES
  var head = document.getElementById("header"); //!BOX FOR THE HEADER
  var riddling = document.getElementById("playsection"); //!BOX FOR PLAY SECTION RIDDLES
  var alert=document.getElementById("alerts");//!THE ALERT BOX
  
  
  /******
   ** THIS FUNCTION IS IN CHARGE OF HIDING PLAYERS NAME INPUT BOX AND BRINGING THE RIDDLE SECTION
   */
  function hide() {
    containerr.classList.add("remover");
    head.classList.remove("remover");
    riddling.classList.remove("remover");
  }
  function alerts(){
    alert.classList.toggle("remover");
  }
  /***
   * *THIS FUNCTION CHECKS IF THE PLAYERS HAVE FILLED THEIR NAMES BEFORE PROCEEDING
   */
  
  let player1;
  let player2;
  function checkerss() {
    player1 = document.getElementById("playeroness").value;
    player2 = document.getElementById("playertwoo").value;
        
    if (player1 == "" || player2 === "") {
       alerts();
    } else {
      hide();

      //!modal progress bar display
      document.getElementById("playerone10").innerHTML=player1;
      document.getElementById("playerone12").innerHTML=player2;
    }
    
  }
  
  
  /**
   * *THIS FUNCTION IS USED TO GENERATE A RANDOM NUMBER INDEX,THE NUMBER GENERATED WILL BE CHECKED IF IT EXIST IN THE Z ARRAY
   * *IF THE RANDOM NUMBER EXIST IN THE Z ARRAY,ANOTHER NUMBER IS GNERATED,THE NUMBER THAT DO NOT EXIST IN THE Z ARRAY WILL RETURNED AS INDEXED
   * *ALSO THE NUMBER WILL BE PUT IN THE Z ARRAY,SO AS TO PREVENT THE NUMBER FROM BEING PICKED AGAIN.
   *
   * !THE Z ARRAY ALSO WILL BE USED IN THE GAMEOVER FUNCTION,THE GAMEOVER FUNCTION IS A FUNCTION IS CALLED TO SHOW THE GAME HAS ENDED
   */

  let z = [];
  function random() {
    do {
      var index = Math.floor(Math.random() * riddles.length);
    } while (z.indexOf(index) > -1);
    z.unshift(index);
  
    return index;
  }
  
  /**
   ** THIS IS IN CHARGE OF DISPLAYING THE RIDDLE TO THE PLAYER ,IT PICK A RANDOM NUMBER BY CALLING THE RANDOM FUNCTION TO GENERATE A RANDOM NUMBER
   * * WHICH WILL USED AS INDEX IN THE RIDDLE ARRAY TO PICK A RIDDLE.
   *
   * !THE RANDOMANSWER VARIABLE IS USED TO SHOW THE RANDOM NUMBER OR INDEX OF THE RIDDLE PICKED SO THAT IT WILL USED IN CHECKING FOR
   * !ANSWER FOR THAT SINCE THE RIDDLE ARRAY AND ANSWER ARRAY ARE PARALLEL TO EACH OTHER
   */
  
  var randomanswer;
  var Driddle = document.getElementById("riddle");
  function displayRiddle(number) {
    Driddle.innerHTML = riddles[number];
    randomanswer = number;
  }
  
  //*SCORE AREA,PROGRESS  AND PLAYER DISPLAY AREA
  let currentPlayer=player1;
  let player1score=0;
  let player2score=0;
  let currentscore = player1score;
  
  function scoreadder(){
      if(currentPlayer === player1){
          player1score+=10;
          currentscore = player1score;
      }else{
          player2score+=10;
      currentscore =player2score;
      }
      document.getElementById("score").textContent =":"+ currentscore; 

      let scoring=player1score.toString()+"%";
      let scoring2=player2score.toString()+"%";


      document.getElementById("progress1").style.width= scoring;
      document.getElementById("progress2").style.width= scoring2;

  }
  
  function changePlayer() {
    if (currentPlayer === player1) {
      currentPlayer = player2;
      currentscore=player2score;
    } else {
      currentPlayer = player1;
      currentscore=player1score;
    }
    document.getElementById("playername").textContent=currentPlayer.toUpperCase(); 
    document.getElementById("score").innerHTML =" :" + currentscore;
    
  }
  
  
  
  //*END OF SCORE AREA;
  
  
  /**
   **THIS FUNCTION IS CALLED WHEN A USER GETS THE RIDDLE RIGHT IT TO APPLAUSE THE USER FOR GETTING IT RIGHT
   */
  
   var y = document.getElementById("correctdisplay");
   function displaycorrect() {
     var y = document.getElementById("correctdisplay");
     y.style.color = "green";
     var teds = [
       "CORRECT!",
       "YOU NAILED IT!",
       "SPOT ON!",
       "SUPERB!",
       "KEEP IT UP",
     ];
     let t = Math.floor(Math.random() * teds.length);
      return y.textContent = teds[t];
   }
  
   
   
   //*THIS FUNCTION DELETE THE DISPLAYCORRECT FUNCTION WHEN A NEW RIDDLE IS DISPLAYED (WHEN USER CLICK NEXT BUTTON)
   
  function delit() {
    return (y.textContent = "");
  }
  
  /***
   * *THIS FUNCTION IS USED TO GET THE USER ANSWER FOR A RIDDLE
   */
  
  function getUserInput() {
    var userInput = document.getElementById("useranswer").value;
    return userInput;
  }
  
  /******
        **THIS FUNCTION IS USED TO CLEAR THE INPUT BOX WHEN THE USER CLICK NEXT FOR THE NEXT RIDDLE
        *****/
    
  var userInput = document.getElementById("useranswer");
  function clearresponse() {
    return (userInput.value = "");
  }
  
  /**
   **THIS IS THE CHECKIT FUNCTION IT IS IN CHARGE OF CHECKING IF THE USER ANSWER IS RIGHT,IF IT IS RIGHT IT CALLS SCORE FUNCTION TO ADD THE SCORE
   * *AND DISPLAYCORRECT TO GENERATE A RANDOM WORD OF TELLING THE PLAYER HIS ANSWER IS CORRECT
   * !THE RANDOMANSWER VARIABLE IS USED AS ARGUEMENT AS TO SHOW THE INDEX OF THE ANSWER REMEMBER RIDDLE AND ANSWER ARRAY ARE PARALLEL MEANING THE INDEX OF A RIDDLE IS THE SAME AS THE INDEX OF ITS ANSWER.
   *
   */
  
  function checkit(answer) {
    if (answers[randomanswer].test(answer)) {
      scoreadder();
      displaycorrect();
    } else {
      correction(randomanswer);
    }
  }
  
  /**********************
           *?WE PUT THE CHECKIT FUNCTION IN THIS ONLYONCE FUNCTION SO THAT IT CAN ONLY BE CALLED ONLY ONCE PER RIDDLE
           *?THE ONLYONCE FUNCTION USES THE VARIABLE CHECKONCE AS FLAG IN THAT WHEN IT CALLED IT CHECKS IF THE CHECKONCE VARIABLE IF IT IS FALSE OR TRUE,
           *?WHEN IT IS FALSE IT NOW CALLS THE CHECKIT FUNCTION TO DO IT THING ,IT ALSO SET THE CHECKONCE VARIABLE TO TRUE SO THAT WHEN IT IS TRIED TO BE CALLED IT WILL 
           *?FIND THE CHECKONCE VARIABLE AS TRUE THUS IT WON'T THE CHECKIT  FUNCTION AGAIN MAKING THE CHECKIT FUNCTION TO BE CALLED ONLY ONCE.
           ****************/

        
  let checkonce = false; //this is a flag used to call a function
  function onlyonce() {
    if (!checkonce) {
      checkit(getUserInput());
      checkonce = true;
    }
  }
  
  /************
   ** THIS FUNCTION IS IN CHARGE WHEN THE PLAYER CLICKS  NEXT BUTTON IT RESET THE CHECKONCE VARIABLE TO FALSE TO ENABLE THE CHECKIT FUNCTION TO BE ONCE AGAIN FOR A NEW RIDDLE
   *************/
  
  function resetcheckonce() {
    return (checkonce = false);
  }
  
  /**
   * *THIS FUNCTION IS CALLED IN THE CHECKIT FUNCTION WHEN THE PLAYERS GIVES INCORRECT ANSWER,WHEN CALLED IT IS PASSES THE RANDOMANSWER AS ARGUMENT
   * *TO SHOW THE INDEX OF THE PARABLE SO AS IT CAN GIVE OUT THE SOLUTION AS PER INDEX
   *
   */
  
  function correction(x) {
    var correct;
    if (x === 0) {
      correct = "All months have 28days";
    } else if (x === 1) {
      correct = "Doorbell";
    } else if (x === 2) {
      correct = 'The answer is seven(if you take away "s" it becomes "even"';
    } else if (x === 3) {
      correct = "Clock";
    } else if (x === 4) {
      correct = "Trouble";
    } else if (x === 5) {
      correct = "Candle";
    } else if (x === 6) {
      correct = "Sponge";
    } else if (x === 7) {
      correct = "none because all weigh the same";
    } else if (x === 8) {
      correct = "letter W is at the end of  rainbow";
    } else if (x === 9) {
      correct = "Age";
    } else if (x === 10) {
      correct = "A promise";
    } else if (x === 11) {
      correct = "What is the time?";
    } else if (x === 12) {
      correct = 'Letter "e"';
    } else if (x === 13) {
      correct = 'Letter "n"';
    } else if (x === 14) {
      correct = "In a clock(add two hours to 11 o'clock)";
    } else if (x === 15) {
      correct = "Silence";
    } else if (x === 16) {
      correct = "Your lefthand";
    } else if (x === 17) {
      correct = "Your shadow";
    } else if (x === 18) {
      correct = "A middle name";
    } else if (x === 19) {
      correct = "The word Wrong";
    } else if (x === 20) {
      correct = "Lettet F";
    } else if (x === 21) {
      correct = "An egg";
    } else if (x === 22) {
      correct = "Europe";
    } else if (x === 23) {
      correct = "Echo";
    } else if (x === 24) {
      correct = "light";
    } else if (x === 25) {
      correct = "Echo";
    } else if (x === 26) {
      correct = "Candle";
    } else if (x === 27) {
      correct = "None is lying because the three doctors are paul's sisters";
    } else if (x === 28) {
      correct = "They are eleven letters in the word alphabet";
    } else if (x === 29) {
      correct = "Towel";
    } else if (x === 30) {
      correct = "Map";
    } else if (x === 31) {
      correct = "All the people in boat were married";
    } else if (x === 32) {
      correct = "Five children(all the four daughter have the same brother)";
    } else if (x === 33) {
      correct = "River";
    } else if (x === 34) {
      correct = "Electric train don't emit smoke";
    } else if (x === 35) {
      correct = "Only one brother";
    } else if (x === 36) {
      correct = "They are grandfather,father and son";
    } else if (x === 37) {
      correct =
        "The room with the lions beacause if they haven't eaten in years then they're already dead";
    } else if (x === 38) {
      correct = "Fishhook";
    } else if (x === 39) {
      correct =
        "The chef because steve was murdered in the afternoon yet the chef's alibi was that he was making breakfast";
    } else if (x === 40) {
      correct = "Survivors are not buried";
    } else if (x === 41) {
      correct = "Footsteps";
    } else if (x === 42) {
      correct = "Fire";
    } else if (x === 43) {
      correct = "A cold";
    } else if (x === 44) {
      correct = "Plate and Spoon";
    } else if (x === 45) {
      correct = "An egg";
    } else if (x === 46) {
      correct = "Feather";
    } else if (x === 47) {
      correct = "Age";
    } else if (x === 48) {
      correct = "He was walking";
    } else if (x === 49) {
      correct = "Wheat(heat,eat,tea)";
    } else if (x === 50) {
      correct = "widow(She knows the husband is always on the grave)";
    } else if (x === 51) {
      correct = "You don't ever knock at your hotel room";
    } else if (x === 52) {
      correct = "nine";
    } else if (x === 53) {
      correct = "Zero";
    } else if (x === 54) {
      correct = "They are triplets";
    } else if (x === 55) {
      correct = "hair";
    } else if (x === 56) {
      correct = "A priest";
    } else if (x === 57) {
      correct = "Turn 30";
    } else if (x === 58) {
      correct = "Nothing";
    } else if (x === 59) {
      correct = "stars";
    } else if (x === 60) {
      correct = "Future";
    } else if (x === 61) {
      correct = "Road";
    } else if (x === 62) {
      correct = "lunch and supper";
    } else if (x === 63) {
      correct = "Tomorrow";
    } else if (x === 64) {
      correct = "Heroine";
    } else if (x === 65) {
      correct = 'Queue.Remove the "ueue" and you are left with "Q"';
    } else if (x === 66) {
      correct =
        "The nail would be at the same height since trees grow at their tops";
    } else if (x === 67) {
      correct = "I am 40 and my daughter and my daughter is 10";
    } else if (x === 68) {
      correct =
        "Day 19,it is not 10 beacause on day 20 she doubled from day 19,so 19 must be half the size of the pond";
    } else if (x === 69) {
      correct = "Age";
    } else if (x === 70) {
      correct = "secret";
    }else if(x===71){
      correct="Concrete floors are very hard to crack";
    }else if(x === 72){
      correct="Your tongue";
    }else if(x === 73){
      correct="A library";
    }else if(x===74 ){
      correct="Palm";
    }else if(x===75){
      correct="Teapot";
    }else if(x===76){
      correct="The dictionary.";
    }else if(x===77){
      correct="The letter g";
    }else if(x===78){
      correct="They're both in the middle of water.";
    }else if(x===79){
      correct="A relationship";
    }else if(x===80){
      correct="wet";
    }else if(x===81){
      correct="A candle";
    }else if(x===82){
      correct="Short";
    }else if(x===83){
      correct="Your shadow";
    }else if(x===84){
      correct="A coin";
    }else if(x===85){
      correct="A comp";
    }else if(x===86){
      correct="A bed";
    }else if(x===87){
      correct="Soap";
    }else if(x===88){
      correct="SWIMS";
    }else if(x===89){
      correct="A plant";
    }else if(x===90){
      correct="Seed, we put seeds in the ground then plants comes out";
    }else if(x===91){
      correct="A chameleon, chameleon seems to stagger as he walks";
    }else if(x===92){
      correct="your picture";
    }else if(x===93){
      correct="Your name";
    }else if(x===94){
      correct="Your breath";
    }else if(x===95){
      correct="His horse is named Friday";
    }else if(x===96){
      correct="Your mother";
    }else if(x===97){
      correct="They were born in different time zones.";
    }else if(x===98){
      correct="A coffin";
    }else if(x===99){
      correct="He is bald";
    }else if(x===100){
      correct="Second place";
    }else if(x===101){
      correct="A match";
    }else if(x ===102){
      correct="seven";
    }else if(x===103){
      correct="27th";
    }else if(x===104){
      correct="eat(ate)"
    }else if(x===105){
      correct="B is A's daughter.";
    }else{
      correct=' The word "and."';
    }
    
    y.style.color = "red";
    return (y.textContent = correct);
  }
  
  /***************
           **SUBMIT AND NEXT BUTTONS
         ***********/

          
  var submit = document.getElementById("submit");
  
  
  /*********************************************
   * !GAMEOVER  SECTIONS
   * !GAMEOVER SECTIONS
   * !GAMEOVER SECTIONS
   *************************************************/
  /****
   * *FUNCTION IN CHARGE OF THE DISPLAYS IN GAMEOVER
   */
  
  function gameoverDisplay() {
    
    
      //*sets the header to tell the player the game is over
      document.querySelector("#gameover").innerHTML = "GAMEOVER";
        
      //*gameover display
      var gameoverd=document.querySelector(".gameoverContainer");
      gameoverd.classList.remove("remover");
    
      //*hides riddle play screen
var riddledisplay=document.getElementById("riddles");
  riddledisplay.classList.add("remover");
  var useranserdisplay=document.querySelector(".useranswer");
  useranserdisplay.classList.add("remover");

      //*creating the winner's name
     var nick=document.getElementById("winnerplayer");
      if (player1score > player2score) {
        nick.innerHTML = player1 + " WINS";
      } else if (player1score < player2score) {
        nick.innerHTML = player2 + " WINS";
      } else {
        nick.innerHTML = "ITS A DRAW";
      }
    
      //*creating player one scoring board:
      document.getElementById("playeroone").innerHTML=player1;
      document.getElementById("scoreone").innerHTML=player1score;
      
      //*creating player two scoring board
      document.getElementById("pllayertwo").innerHTML=player2;
      document.getElementById("scoretwoo").innerHTML= player2score;
    
    }
    
  //?THIS FUNCTION REMOVES THE SCORE AND NAME ELEMENTS(displays)
  
  function namescore(){
  var playername=document.getElementById("playername");
  var sccore = document.getElementById("score");
    playername.remove();
    sccore.remove();
  }
  
  
  
    /********************
      ** THIS FUNCTION DELETES THE SUBMIT AND NEXT BUTTON WHEN THE GAME IS OVER SO AS A NEW BUTTON IS CREATED
       *****************/

    function remover() {
      submit.remove();
      userInput.remove();
      namescore();
    }
    
    /* *********
      *THIS FUNCTION CREATES A BUTTON CALLED BACK TO MENU,THIS BUTTON TAKES PLAYER TO MAIN MENU TO START A NEW GAME AGAIN
      *********/
  
    function backToMenu() {
      var button = document.createElement("button");
      button.textContent = "Back to main menu";
      button.id = "back";
      button.classList.add("btn");
    button.classList.add("btn-sm");
    button.classList.add("btn-md");
    
      var buttonContainer = document.querySelector(".batttlee");
      buttonContainer.appendChild(button);
    
      //*on click takes the use to the main menu
      button.onclick = () => {
        window.location.href = "starterbootstrap.html";
      };
    }
    
    /**
     * !THIS FUNCTION IS THE GAMEOVER FUNCTION THAT IS IN CHARGE OF ALL THE OTHER GAME OVER FUNCTIONS
     */

    function gameover() {
      gameoverDisplay(); //tell the user the game is over and displays the player score
      remover(); //delete submit and next button
      backToMenu(); //create a button to take the user back to the main menu
    }
    
    /**
     * !FUNCTION TO CALL THE GAMEOVER
     */

 
    function master(x) {
      if (x.length > 10) {
        gameover();
      }
    }
    
    /**
     * *THIS FUNCTION automatic IS CALLED ONLY WHEN THE KEY DOWN (ENTER )IS PRESSED SO AS TO ENABLE THE PLAYER WHICH CARRY OUT THE FUNCTION OF NEXT BUTTON
     */
    //! polices is in charge to avoid conflict between submit button and enter key
      //!polices will act as a flag in that when a user presses submit key the enter key will be in active
      //!as well for the enter key it will make submit inactive.
     
      let police1=false;
      let police2=false;
  
    function automatic() {
      displayRiddle(random()); //*displays the riddles
      clearresponse(); //*clear response of the user for next riddle
      delit(); //*delete the displaycorrect for next riddle
      resetcheckonce(); //*reset checkonce
      master(z); //*master to call gameover
      changePlayer();//*change currentplayer
      //!we set the police variable false for any preference 
       police1=false;
       police2=false;
    }
    
    window.onload = () => {
  
      battlebutton.onclick = () => {
        checkerss(); //*function to check if players have filled their names before proceeding to call the hide function
        changePlayer();
      };
    
      displayRiddle(random()); //*display the current riddle
       
      //*when called it set police2 true to avoid enter being called
      submit.onclick = () => {
        if(!police1){
        onlyonce(); //!It emmbedds the checkit function that is in charge of checking the user input ,the onlyonce enables the checkit function to only be called once
        setTimeout(automatic, 4000);
         police2=true;
        }
      };
      
      /****
       * !USING KEY DOWN EVENT TO SUBMIT AND NEXT A RIDDLE
       * ?when called it set police! true to avoid it being called
       */
      
      document.addEventListener("keydown", (event) => {
        if (event.key == "Enter") {
          if(!police2){
          onlyonce();
          setTimeout(automatic, 4000);
          police1=true;
          }
        }
      });



      
      
      
    };
    
  
  