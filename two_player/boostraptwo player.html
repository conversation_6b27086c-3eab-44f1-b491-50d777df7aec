<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Title</title>
    <!-- Required meta tags -->
    
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
       <link rel="stylesheet" href="boostraptwo player.css">
    <!-- Bootstrap CSS v5.2.1 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
  </head>

  <body>
    <div class="container-fluid">
      <!--!START OF HEAD SECTION-->
      <div class="head remover" id="header">
     <div class="row"><!--!remover class-->
      <div class="col-4">
      <div class="scoreboard d-flex flex-row">
        <p id="playername" class="text-sm text-md fs-4 mt-2"> </p>
        <p id="score" class="text-sm text-md fs-4 mt-2"> </p>
      </div>
      </div>

      <div class="col-4">
        <div class="gameover d-sm-flex align-items-center">
          <p   id="gameover" class="text-sm text-md fs-2 text-center"></p>
        </div>
      </div>



      <div class="col-4 d-flex justify-content-end">
        <button
        type="button"
        class="btn btn-danger btn-sm btn-md h-50 mt-2 "
        data-bs-toggle="modal"
        data-bs-target="#myModal"
        id="modalbutton"
         >
        STATS
       </button>
       </div> 

    </div>
    
  </div>
    <!--!END OF HEAD SECTION-->

     <!--! The Modal -->
     <div class="modal" id="myModal">
      <div class="modal-dialog">
        <div class="modal-content">

          <!-- ?Modal Header -->
          <div class="modal-header">
            <h4 class="modal-title">STATS</h4>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
            ></button>
          </div>

          <!-- ?Modal body -->
          <div class="modal-body">
              <div class="progress" style="height: 20%;">

                  <div
                    class="progress-bar progress-bar-striped progress-bar-animated"
                    role="progressbar"
                    aria-valuenow="25"
                    aria-valuemin="0"
                    aria-valuemax="100"
                    id="progress1"
                  >
                  <p class="text-danger fs-4"></p>
                  </div>
            
                </div> 
                <p id="playerone10" class="text-danger "></p>

               
                <div class="progress mt-2" style="height: 20%;">
                  <div
                    class="progress-bar progress-bar-striped progress-bar-animated bg-danger"
                    role="progressbar"
                    aria-valuenow="25"
                    aria-valuemin="0"
                    aria-valuemax="100"
                    id="progress2"
                  >
                  <p class="text-danger fs-4"></p>
                  </div>
                </div>
                <p id="playerone12" class="text-danger"></p

          </div>

          <!-- ?Modal footer -->
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-danger"
              data-bs-dismiss="modal"
            >
              Close
            </button>
          </div>

        </div>
      </div>
    </div>

</div>

<!--!ALERT-->
<div class="alert remover" id="alerts"><!--!remover class-->
    <div
      class="alert alert-danger alert-dismissible fade show"
      role="alert"
    >
      <button
        type="button"
        class="btn-close"
        data-bs-dismiss="alert"
        aria-label="Close"
      ></button>
    
      <strong>Please enter your names!</strong> before proceeding to battles.
    </div>
</div>
<!--!END OF ALERT-->


<!--!REGFORM FOR NAMES-->
<div class="container regform" id="regform"><!--//remover class-->
  <p id="introhead">RIDDLE BATTLE</p>
  <div class="row">
  <div class="col-lg-6 col-sm-12">
  <div class="mb-3">
    <label for="playerone" class="form-label text-danger fw-bold fs-3">Player One</label>
    <input
      type="text"
      name="playerone"
      id="playeroness"
      class="form-control"
      placeholder="player one"
      aria-describedby="helpId"
      required
    />
  </div>
  </div>

  <div class="col-lg-6 col-sm-12">
  <div class="mb-3 ">
    <label for="playertwo" class="form-label text-danger fw-bold fs-3">player two</label>
    <input
      type="text"
      name=""
      id="playertwoo"
      class="form-control"
      placeholder="player two"
      aria-describedby="helpId"
      required
    />
  </div>
</div>

<div class="buttonbattle d-flex justify-content-center ">
  <button
    type="button"
    class="btn btn-sm"
    id="battle"
  >
    BATTLE
  </button>
</div>
  </div>
</div>
<!--!END OF REGFORM-->


<!--!PLAY SECTION-->
<div class="container riddle remover" id="playsection"><!--!remover class-->
  <div class="row">
    <div class="col-12">
      <div class="riddlecontent">

        <!--!GAME OVER GAME OVER  GAMEOVER DISPLAY-->
        <div class="gameoverContainer remover">

          <div class="imager">
            <img
              src="real_number_1-removebg-preview.png"
              class="img-fluid rounded-top"
              alt="images"
              id="image"
            />
          </div>

          <div class="winner">
            <p id="winnerplayer"></p>
          </div>
           

          <div class="statistics">
            <div
              class="table-responsive"
            >
              <table
                class="table table-primary table-striped table-dark table-hover table-bordered"
              >
                <thead>
                  <tr>
                    <th scope="col">PLAYERS</th>
                    <th scope="col">SCORES</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="">
                    <td scope="row" id="playeroone"></td>
                    <td id="scoreone"></td>
                    
                  </tr>
                  <tr class="">
                    <td scope="row" id="pllayertwo"></td>
                    <td id="scoretwoo"></td>
                  </tr>

                </tbody>
              </table>
            </div>
          </div>
        </div>
        <!--!END OF GAME OVER CONTAINER-->

 
         <!--!RIDDLE -->
        <div class="containersriddle " id="riddles"><!--!remover class-->
          <p id="riddle" class="text-sm text-md ">
            
          </p>
        </div>


        <div class="useranswer">
          <div class="mb-3">
            <label for="useranswer" class="form-label"></label>
            <input
              type="text"
              name="useranswer"
              id="useranswer"
              class="form-control"
              placeholder="Enter your answer"
            />
          </div>
        </div>


        <div class="displayCorrect">
          <p id="correctdisplay" class="text-sm text-md fs-3 fw-bold "></p>
        </div>


        <div class="batttlee">
          <button
            type="button"
            class="btn btn-primary w-50"
            id="submit"
          >
            submit
          </button>
        </div>
          <!--!END OF RIDDLE-->

      </div>
    </div>
  </div>
</div>
<!--!END OF PLAY SECTION-->




    


  </div>

  
    













<script src="bootstraptwo player.js"></script>
    <script
      src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
      integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r"
      crossorigin="anonymous"
    ></script>

    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.min.js"
      integrity="sha384-BBtl+eGJRgqQAUMxJ7pMwbEyER4l1g+O15P+16Ep7Q9Q+zqX6gSbd85u4mG4QzX+"
      crossorigin="anonymous"
    ></script>
  </body>
</html>
