body{
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: center;
    background-image: url(../images/dark\ forest\ picture.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    height: 100vh;
}
h1{
    font-size: 40px;
	font-weight: 1000;
    color: transparent; 
    -webkit-text-stroke: 1px white;
    background-image: url(../images/red.jpg);
    -webkit-background-clip: text;
    background-position: 0 0;
    animation: back 20s linear infinite;
    text-align: center;
}
@keyframes back{
    100%{
        background-position: 2000px 0;
    }
}
p{
    color:red; /* White text color */
    font-family: 'Vampiro One', cursive
}
#containters{
    width:70vw;
    height: 80vh;
    margin-top: 7%;
    display: flex;
    align-items: center;
    justify-content:  center;
    flex-direction: column;
    
}
.btn{
    padding: 10px;
    margin: 5px;
    width: 25%;
    border-radius: 10px;
    font-weight: 900;
    background-image: url(../images/red.jpg);
    background-repeat: no-repeat;
    background-size: cover;

}
.btn:hover{
    color: #666666;
}

@media screen and (max-width: 768px){
h1{
    font-size: 25px;
}
.btn{
    width:50%;
}

}