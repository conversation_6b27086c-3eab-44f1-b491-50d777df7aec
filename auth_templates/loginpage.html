<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <title>Riddle Realm of Dread - Login</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />

    <!-- Bootstrap CSS v5.3.2 -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />
       <link rel="stylesheet"  href="../css/loginpage.css">
       <script defer src="../js/loginpage.js"></script>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="login-container">
                    <h1>Riddle Realm of Dread</h1>
                    <p class="text-sm">
                        Enter the Realm of Mysteries... If You Dare
                    </p>
                    
                    <form id="login-form" class="login-form">
                        <!-- Error Message Div -->
                        <div id="error-message" class="error-message">
                            Incorrect username or password. Please try again.
                        </div>

                        <input type="text" id="username" class="form-control" placeholder="Username" required>
                        <input type="password" id="password" class="form-control" placeholder="Password" required>
                        
                        <button type="submit" class="btn btn-login">Login</button>

                        <!-- Social Login Section -->
                        <div class="social-login">
                            <p class="divider"><span>Or continue with</span></p>
                            <div class="social-buttons">
                                <button type="button" class="btn btn-social btn-google">
                                    <i class="fab fa-google"></i> Google
                                </button>
                                <button type="button" class="btn btn-social btn-facebook">
                                    <i class="fab fa-facebook-f"></i> Facebook
                                </button>
                            </div>
                        </div>
                        
                        <div class="login-links">
                            <button type="button" id="btn-signup" class="btn btn-secondary">Sign Up</button>
                            <button type="button" id="btn-guest" class="btn btn-secondary">Play as Guest</button>
                            <a href="#" id="forgot-password" class="login-link">Forgot Password?</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Forgot Password Modal -->
    <div class="modal fade" id="forgotPasswordModal" tabindex="-1" aria-labelledby="forgotPasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="forgotPasswordModalLabel">Recover Your Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="forgot-password-form">
                        <input type="email" id="recovery-email" class="form-control" placeholder="Enter your email" required>
                        <small class="text-muted mt-2 d-block">We'll send a password reset link to your email.</small>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" id="send-recovery-btn" class="btn btn-login">Send Recovery Link</button>
                </div>
            </div>
        </div>
    </div>

  
    
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.min.js"></script>
</body>
</html>