<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Profile - Riddle Master</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Crimson+Pro:wght@400;700&family=Pirata+One&display=swap');

:root {
    --dark-primary: #1a1a1a;
    --dark-secondary: #2a2a2a;
    --accent-red: #8b0000;
    --accent-gold: #b8860b;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --shadow-red: rgba(139, 0, 0, 0.5);
}

body {
    background: linear-gradient(rgba(0,0,0,0.8), rgba(0,0,0,0.8)),
                url('../images/dark-texture.jpg') center/cover fixed;
    color: var(--text-primary);
    font-family: 'Crimson Pro', serif;
    margin: 0;
    min-height: 100vh;
}

.profile-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.profile-header {
    background: var(--dark-secondary);
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 0 20px var(--shadow-red);
}

.header-content {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content h1 {
    font-family: 'Pirata One', cursive;
    color: var(--accent-red);
    margin: 0;
    font-size: 2.5rem;
    text-shadow: 0 0 10px var(--shadow-red);
}

/* Main Content Layout */
.profile-content {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 30px;
}

/* User Info Panel */
.user-info-panel {
    background: var(--dark-secondary);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 0 20px var(--shadow-red);
}

.stats-overview {
    text-align: center;
    margin-bottom: 30px;
}

.total-stats {
    padding: 20px;
    background: var(--dark-primary);
    border-radius: 10px;
    border: 1px solid var(--accent-red);
}

.stat-number {
    font-size: 3rem;
    color: var(--accent-gold);
    font-weight: bold;
}

.stat-breakdown {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-top: 15px;
    font-size: 0.9rem;
}

/* Avatar Grid */
.avatar-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.avatar-option {
    aspect-ratio: 1;
    border-radius: 50%;
    border: 2px solid var(--accent-red);
    cursor: pointer;
    transition: all 0.3s ease;
    background-size: cover;
}

.avatar-option:hover {
    transform: scale(1.1);
    box-shadow: 0 0 15px var(--shadow-red);
}

.avatar-option.selected {
    border-color: var(--accent-gold);
    transform: scale(1.1);
}

/* Settings Panel */
.settings-panel {
    background: var(--dark-secondary);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 0 20px var(--shadow-red);
}

/* Difficulty Stats */
.difficulty-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.diff-card {
    background: var(--dark-primary);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    border: 1px solid var(--accent-red);
}

.progress-ring {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 15px auto;
    border: 3px solid var(--accent-red);
}

.progress-number {
    font-size: 2rem;
    color: var(--accent-gold);
}

/* Account Settings */
.account-settings {
    background: var(--dark-primary);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid var(--accent-red);
}

.settings-form {
    display: grid;
    gap: 20px;
}

.form-group {
    display: grid;
    gap: 10px;
}

/* Buttons */
.btn-theme, .btn-change, .btn-save, .btn-cancel {
    background: linear-gradient(45deg, var(--accent-red), var(--dark-primary));
    color: var(--text-primary);
    border: 1px solid var(--accent-red);
    padding: 8px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-theme:hover, .btn-change:hover, .btn-save:hover, .btn-cancel:hover {
    transform: scale(1.05);
    box-shadow: 0 0 10px var(--shadow-red);
}

/* Footer */
.profile-footer {
    margin-top: 30px;
    display: flex;
    justify-content: center;
    gap: 20px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .profile-content {
        grid-template-columns: 1fr;
    }
    
    .user-info-panel {
        max-width: 600px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .difficulty-stats {
        grid-template-columns: 1fr;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .profile-container {
        padding: 10px;
    }
    
    .avatar-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-breakdown {
        grid-template-columns: 1fr;
    }
    
    .profile-footer {
        flex-direction: column;
    }
    
    .btn-theme, .btn-change, .btn-save, .btn-cancel {
        width: 100%;
    }
}
    </style>
</head>
<body>
    <div class="profile-container">
        <header class="profile-header">
            <div class="header-content">
                <h1>Profile Settings</h1>
                <button class="btn-theme">← Return</button>
            </div>
        </header>

        <main class="profile-content">
            <section class="user-info-panel">
                <div class="stats-overview">
                    <div class="total-stats">
                        <h2>Total Riddles Solved</h2>
                        <div class="stat-number">71</div>
                        <div class="stat-breakdown">
                            <span>🧮 Math: 25</span>
                            <span>📝 Word: 21</span>
                            <span>🤔 Logic: 25</span>
                        </div>
                    </div>
                </div>

                <div class="profile-identity">
                    <div class="avatar-grid">
                        <div class="avatar-option" data-avatar="1"></div>
                        <div class="avatar-option" data-avatar="2"></div>
                        <div class="avatar-option" data-avatar="3"></div>
                        <div class="avatar-option selected" data-avatar="4"></div>
                        <div class="avatar-option" data-avatar="5"></div>
                        <div class="avatar-option" data-avatar="6"></div>
                    </div>
                    <div class="identity-fields">
                        <input type="text" class="username-field" value="RiddleMaster" placeholder="Username">
                        <p class="edit-hint">Click to edit username</p>
                    </div>
                </div>
            </section>

            <section class="settings-panel">
                <div class="difficulty-stats">
                    <div class="diff-card novice">
                        <h3>Novice</h3>
                        <div class="progress-ring">
                            <span class="progress-number">34</span>
                        </div>
                        <div class="category-stats">
                            <div>Math: 12</div>
                            <div>Word: 10</div>
                            <div>Logic: 12</div>
                        </div>
                    </div>

                    <div class="diff-card adept">
                        <h3>Adept</h3>
                        <div class="progress-ring">
                            <span class="progress-number">22</span>
                        </div>
                        <div class="category-stats">
                            <div>Math: 8</div>
                            <div>Word: 7</div>
                            <div>Logic: 7</div>
                        </div>
                    </div>

                    <div class="diff-card master">
                        <h3>Master</h3>
                        <div class="progress-ring">
                            <span class="progress-number">15</span>
                        </div>
                        <div class="category-stats">
                            <div>Math: 5</div>
                            <div>Word: 4</div>
                            <div>Logic: 6</div>
                        </div>
                    </div>
                </div>

                <div class="account-settings">
                    <div class="settings-section">
                        <h2>Account Security</h2>
                        
                        <div class="settings-form">
                            <div class="form-group">
                                <label>Email Address</label>
                                <input type="email" value="<EMAIL>" readonly>
                                <button class="btn-change">Change Email</button>
                            </div>

                            <div class="form-group">
                                <label>Password</label>
                                <input type="password" value="••••••••" readonly>
                                <button class="btn-change">Change Password</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <footer class="profile-footer">
            <button class="btn-save">Save Changes</button>
            <button class="btn-cancel">Cancel</button>
        </footer>
    </div>
</body>
</html>