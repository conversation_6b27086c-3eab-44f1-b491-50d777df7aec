import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  Easing,
} from 'react-native-reanimated';

const AnimatedCard = ({ icon, title, description, delay }) => {
  const translateY = useSharedValue(0);

  React.useEffect(() => {
    translateY.value = withRepeat(
      withTiming(-15, {
        duration: 3000,
        easing: Easing.inOut(Easing.ease),
      }),
      -1,
      true,
      delay
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  return (
    <Animated.View style={[styles.card, animatedStyle]}>
      <Text style={styles.cardIcon}>{icon}</Text>
      <Text style={styles.cardTitle}>{title}</Text>
      <Text style={styles.cardDescription}>{description}</Text>
    </Animated.View>
  );
};

export default function GameShowcase() {
  return (
    <View style={styles.gameShowcase}>
      <AnimatedCard
        icon="🔍"
        title="Truth"
        description="Reveal secrets and discover hidden truths"
        delay={0}
      />
      <AnimatedCard
        icon="🎯"
        title="Dare"
        description="Accept thrilling challenges"
        delay={2000}
      />
      <AnimatedCard
        icon="🧩"
        title="Riddle"
        description="Solve mind-bending puzzles"
        delay={4000}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  gameShowcase: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 20,
    marginVertical: 20,
  },
  card: {
    backgroundColor: 'rgba(20, 20, 35, 0.8)',
    borderRadius: 20,
    paddingVertical: 30,
    paddingHorizontal: 25,
    width: 150,
    height: 200,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'rgba(100, 255, 218, 0.2)',
    overflow: 'hidden',
  },
  cardIcon: {
    fontSize: 44,
    marginBottom: 15,
  },
  cardTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#64ffda',
    marginBottom: 10,
    textTransform: 'uppercase',
    letterSpacing: 1,
  },
  cardDescription: {
    fontSize: 10,
    color: '#b0bec5',
    textAlign: 'center',
    lineHeight: 20,
  },
});
