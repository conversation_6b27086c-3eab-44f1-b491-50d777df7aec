import React, { useEffect } from 'react';
import { Text, TouchableOpacity, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
  withRepeat,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';


export default function CTASection() {
  const translateY = useSharedValue(50);
  const opacity = useSharedValue(0);
  const float = useSharedValue(0);
  const router = useRouter();

  useEffect(() => {
    // Slide Up Animation
    opacity.value = withTiming(1, { duration: 1000, easing: Easing.out(Easing.ease), delay: 1500 });
    translateY.value = withTiming(0, { duration: 1000, easing: Easing.out(Easing.ease), delay: 1500 });

    // Floating Button Animation
    float.value = withRepeat(
      withTiming(-5, { duration: 2000, easing: Easing.inOut(Easing.ease) }),
      -1,
      true
    );
  }, []);

  const ctaStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ translateY: translateY.value }],
  }));

  const floatStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: float.value }],
  }));

  return (
    <Animated.View style={[styles.ctaSection, ctaStyle]}>
      <Text style={styles.ctaText}>Ready for the Ultimate Adventure?</Text>
      <Animated.View style={floatStyle}>
        <TouchableOpacity activeOpacity={0.9} onPress={() => router.push('/signup/signup')} >
          <LinearGradient
            colors={['#64ffda', '#00bcd4', '#ff6b9d']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.enterBtn}
          >
            <Text style={styles.enterText}>Enter the Quest</Text>
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  ctaSection: {
    marginTop: 10,
    marginBottom:30,
    alignItems: 'center',
  },
  ctaText: {
    fontSize: 18,
    color: '#eceff1',
    marginBottom: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  enterBtn: {
    paddingVertical: 18,
    paddingHorizontal: 40,
    borderRadius: 50,
    elevation: 4,
    shadowColor: '#64ffda',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.4,
    shadowRadius: 30,
  },
  enterText: {
    color: '#1a1a2e',
    fontSize: 18,
    fontWeight: 'bold',
    textTransform: 'uppercase',
    letterSpacing: 1,
    textAlign: 'center',
  },
});
