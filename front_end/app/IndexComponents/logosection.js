import React, { useEffect } from 'react';
import {StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
} from 'react-native-reanimated';

import GradientText from './logoo'

export default function LogoSection() {
  const translateY = useSharedValue(50);
  const opacity = useSharedValue(0);
  const glowOpacity = useSharedValue(0.5);

  useEffect(() => {
    translateY.value = withTiming(0, {
      duration: 1500,
      easing: Easing.out(Easing.ease),
    });
    opacity.value = withTiming(1, {
      duration: 1500,
      easing: Easing.out(Easing.ease),
    });

    glowOpacity.value = withTiming(1, {
      duration: 2000,
      easing: Easing.inOut(Easing.ease),
    });
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
    opacity: opacity.value,
    marginBottom: 40,
  }));

  const glowStyle = useAnimatedStyle(() => ({
    textShadowColor: `rgba(100, 255, 218, ${glowOpacity.value})`,
  }));

  return (
    <Animated.View style={[styles.logoSection, animatedStyle]}>
      {/* Gradient Text via MaskedView */}
      <GradientText/>
     

      {/* Subtitle with fade + glow */}
      <Animated.Text style={[styles.subtitle, glowStyle]}>
        Truth, Dare & Mystery Await
      </Animated.Text>

      {/* Tagline with soft fade-in */}
      <Animated.Text style={styles.tagline}>
        Challenge Your Mind, Test Your Courage
      </Animated.Text>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  logoSection: {
    alignItems: 'center',
    textAlign: 'center',
  },
  mainLogo: {
    fontSize: 40,
    fontWeight: 'bold',
    textAlign: 'center',
    color: 'transparent', // important for MaskedView
  },
  subtitle: {
    fontSize: 18,
    color: '#b0bec5',
    fontStyle: 'italic',
    marginTop: 10,
    textAlign: 'center',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 20,
  },
  tagline: {
    fontSize: 14,
    color: '#78909c',
    marginTop: 5,
    textAlign: 'center',
  },
});
