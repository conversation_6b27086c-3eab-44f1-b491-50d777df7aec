import React from 'react';
import { View } from 'react-native';
import Svg, { Defs, LinearGradient, Stop, Text } from 'react-native-svg';

const GradientText = () => {
  return (
    <View style={{ alignItems: 'center', marginTop: 40 }}>
      <Svg height="60" width="300">
        <Defs>
          <LinearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#64ffda" />
            <Stop offset="33%" stopColor="#ff6b9d" />
            <Stop offset="66%" stopColor="#c471ed" />
            <Stop offset="100%" stopColor="#12c2e9" />
          </LinearGradient>
        </Defs>
        <Text
          fill="url(#grad)"
          fontSize="40"
          fontWeight="bold"
          x="0"
          y="40"
        >
          🎭 Riddle Quest
        </Text>
      </Svg>
    </View>
  );
};

export default GradientText;
