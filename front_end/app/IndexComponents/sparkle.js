import React from 'react';
import { View, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

export default function Sparkle({ style = {} }) {
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);

  React.useEffect(() => {
    scale.value = withRepeat(
      withTiming(1, { duration: 1500 }),
      -1,
      true
    );
    opacity.value = withRepeat(
      withTiming(1, { duration: 750 }),
      -1,
      true
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      opacity: opacity.value,
    };
  });

  return (
    <Animated.View style={[styles.sparkle, style, animatedStyle]} />
  );
}

const styles = StyleSheet.create({
  sparkle: {
    position: 'absolute',
    width: 4,
    height: 4,
    backgroundColor: '#64ffda',
    borderRadius: 2,
  },
});
