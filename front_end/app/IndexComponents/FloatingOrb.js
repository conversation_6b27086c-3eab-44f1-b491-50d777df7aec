import React from 'react';
import { StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';



const { width, height } = Dimensions.get('window');


export default function FloatingOrb({ size, style, delay = 0 }) {
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const rotate = useSharedValue(0);

  React.useEffect(() => {
    const loop = () => {
      translateX.value = withRepeat(
        withTiming(10, { duration: 2000, easing: Easing.ease }),
        -1,
        true,
        delay
      );
      translateY.value = withRepeat(
        withTiming(-20, { duration: 4000, easing: Easing.ease }),
        -1,
        true,
        delay
      );
      rotate.value = withRepeat(
        withTiming(360, { duration: 8000, easing: Easing.linear }),
        -1,
        false,
        delay
      );
    };

    loop();
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { rotate: `${rotate.value}deg` },
      ],
      opacity:
        rotate.value < 90
          ? 0.6
          : rotate.value < 180
          ? 0.8
          : rotate.value < 270
          ? 1
          : 0.7,
    };
  });

  return (
    <Animated.View style={[styles.orb, style, { width: size, height: size, borderRadius: size / 2 }, animatedStyle]}>
      <LinearGradient
        colors={['rgba(100, 255, 218, 0.3)', 'rgba(100, 255, 218, 0.1)']}
        style={styles.gradient}
        start={[0.5, 0.5]}
        end={[1, 1]}
      />
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  orb: {
    position: 'absolute',
    overflow: 'hidden',
    zIndex: 0,
  },
  gradient: {
    flex: 1,
    borderRadius: 9999,
    filter: 'blur(1px)', // React Native doesn’t support CSS filters
  },
});
