import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
  Modal,
  Pressable,
  StyleSheet,
  Dimensions,
  Animated,
  Easing,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { LinearGradient } from 'expo-linear-gradient';

const { width, height } = Dimensions.get('window');

const RiddleQuestAuth = () => {
  // Animation values
  const floatAnim1 = useRef(new Animated.Value(0)).current;
  const floatAnim2 = useRef(new Animated.Value(0)).current;
  const floatAnim3 = useRef(new Animated.Value(0)).current;
  
  // Start floating animations
  React.useEffect(() => {
    const animateFloating = (animValue, delay) => {
      Animated.loop(
        Animated.sequence([
          Animated.delay(delay),
          Animated.parallel([
            Animated.timing(animValue, {
              toValue: 1,
              duration: 3000,
              easing: Easing.inOut(Easing.ease),
              useNativeDriver: true,
            }),
            Animated.timing(animValue, {
              toValue: 1,
              duration: 6000,
              easing: Easing.linear,
              useNativeDriver: true,
            }),
          ]),
          Animated.timing(animValue, {
            toValue: 0,
            duration: 3000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    animateFloating(floatAnim1, 0);
    animateFloating(floatAnim2, 2000);
    animateFloating(floatAnim3, 4000);
  }, []);

  // State for active tab
  const [activeTab, setActiveTab] = useState('signin');
  
  // Sign In state
  const [signInEmail, setSignInEmail] = useState('');
  const [signInPassword, setSignInPassword] = useState('');
  const [showSignInPassword, setShowSignInPassword] = useState(false);
  const [signInErrors, setSignInErrors] = useState({
    email: '',
    password: '',
  });
  
  // Sign Up state
  const [signUpUsername, setSignUpUsername] = useState('');
  const [signUpEmail, setSignUpEmail] = useState('');
  const [signUpPassword, setSignUpPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showSignUpPassword, setShowSignUpPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [signUpErrors, setSignUpErrors] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [passwordStrength, setPasswordStrength] = useState('');
  
  // Avatar state
  const [selectedAvatar, setSelectedAvatar] = useState(null);
  const [avatarType, setAvatarType] = useState('placeholder'); // 'placeholder', 'emoji', 'image'
  const [showPresetAvatars, setShowPresetAvatars] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // Preset avatars
  const presetAvatars = [
    { emoji: '🙂', color: '#FFB6C1' },
    { emoji: '😊', color: '#6BC9FF' },
    { emoji: '🤔', color: '#FFB86C' },
    { emoji: '😎', color: '#A786FF' },
  ];
  


  // Helper functions
  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const clearAllErrors = () => {
    setSignInErrors({ email: '', password: '' });
    setSignUpErrors({ username: '', email: '', password: '', confirmPassword: '' });
  };

  const checkPasswordStrength = (password) => {
    if (!password) {
      setPasswordStrength('');
      return false;
    }
    
    let strength = 0;
    let feedback = [];
    
    // Length check
    if (password.length >= 6) strength++;
    else feedback.push('At least 6 characters');
    
    // Uppercase check
    if (/[A-Z]/.test(password)) strength++;
    else feedback.push('One uppercase letter');
    
    // Lowercase check
    if (/[a-z]/.test(password)) strength++;
    else feedback.push('One lowercase letter');
    
    // Number check
    if (/\d/.test(password)) strength++;
    else feedback.push('One number');
    
    // Special character check
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
    else feedback.push('One special character');
    
    let strengthText = '';
    
    if (strength < 2) {
      strengthText = `Weak password. Add: ${feedback.slice(0, 3).join(', ')}`;
    } else if (strength < 4) {
      strengthText = `Medium strength. Consider adding: ${feedback.slice(0, 2).join(', ')}`;
    } else {
      strengthText = 'Strong password! 💪';
    }
    
    setPasswordStrength(strengthText);
    return strength >= 2;
  };

  const checkPasswordMatch = () => {
    if (confirmPassword && signUpPassword !== confirmPassword) {
      setSignUpErrors(prev => ({
        ...prev,
        confirmPassword: 'Passwords do not match'
      }));
      return false;
    } else {
      setSignUpErrors(prev => ({
        ...prev,
        confirmPassword: ''
      }));
      return true;
    }
  };

  // Avatar handling
  const handleImageUpload = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 1,
    });

    if (!result.canceled) {
      setSelectedAvatar(result.assets[0].uri);
      setAvatarType('image');
      setShowPresetAvatars(false);
    }
  };

  const selectPresetAvatar = (emoji) => {
    setSelectedAvatar(emoji);
    setAvatarType('emoji');
    setShowPresetAvatars(false);
  };

  // Auth functions
  const signInWithGoogle = () => {
    Alert.alert(
      'Google Sign In',
      '🚀 Google Sign In would be implemented here!\n\nIn a real app, this would use Google OAuth API.'
    );
  };

  const signUpWithGoogle = () => {
    Alert.alert(
      'Google Sign Up',
      '🚀 Google Sign Up would be implemented here!\n\nIn a real app, this would use Google OAuth API.'
    );
  };

  const handleSignIn = async () => {
    clearAllErrors();
    let hasErrors = false;
    const newErrors = { email: '', password: '' };

    // Email validation
    if (!signInEmail) {
      newErrors.email = 'Email is required';
      hasErrors = true;
    } else if (!isValidEmail(signInEmail)) {
      newErrors.email = 'Please enter a valid email address';
      hasErrors = true;
    }

    // Password validation
    if (!signInPassword) {
      newErrors.password = 'Password is required';
      hasErrors = true;
    } else if (signInPassword.length < 6) {
      newErrors.password = 'Password must be at least 6 characters long';
      hasErrors = true;
    }

    setSignInErrors(newErrors);

    if (hasErrors) return;

    setIsLoading(true);

    try {
      // Send login request to backend API
      const response = await fetch('http://********:3001/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: signInEmail,
          password: signInPassword
        })
      });

      const data = await response.json();

      if (data.success) {
        setIsLoading(false);
        Alert.alert(
          'Welcome back!',
          `🎉 Welcome back, ${data.user.username}!\n\nRedirecting to the game...`
        );

        // Clear form
        setSignInEmail('');
        setSignInPassword('');

      } else {
        setIsLoading(false);
        // Handle specific error messages from backend
        const updatedErrors = { ...newErrors };
        if (data.message.includes('email')) {
          updatedErrors.email = data.message;
        } else if (data.message.includes('password')) {
          updatedErrors.password = data.message;
        } else {
          updatedErrors.email = data.message;
        }
        setSignInErrors(updatedErrors);
      }

    } catch (error) {
      setIsLoading(false);
      console.error('Login error:', error);
      Alert.alert(
        'Network Error',
        '❌ Network error. Please check if the server is running and try again.'
      );
    }
  };

  const handleSignUp = async () => {
    clearAllErrors();
    let hasErrors = false;
    const newErrors = {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
    };

    // Username validation
    if (!signUpUsername) {
      newErrors.username = 'Username is required';
      hasErrors = true;
    } else if (signUpUsername.length < 3) {
      newErrors.username = 'Username must be at least 3 characters long';
      hasErrors = true;
    }

    // Email validation
    if (!signUpEmail) {
      newErrors.email = 'Email is required';
      hasErrors = true;
    } else if (!isValidEmail(signUpEmail)) {
      newErrors.email = 'Please enter a valid email address';
      hasErrors = true;
    }

    // Password validation
    if (!signUpPassword) {
      newErrors.password = 'Password is required';
      hasErrors = true;
    } else if (signUpPassword.length < 6) {
      newErrors.password = 'Password must be at least 6 characters long';
      hasErrors = true;
    } else if (!checkPasswordStrength(signUpPassword)) {
      newErrors.password = 'Please create a stronger password';
      hasErrors = true;
    }

    // Confirm password validation
    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
      hasErrors = true;
    } else if (signUpPassword !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
      hasErrors = true;
    }

    setSignUpErrors(newErrors);

    if (hasErrors) return;

    setIsLoading(true);

    try {
      // Send data to backend API
      // For React Native, use your computer's IP address instead of localhost
      // You can find it by running: ipconfig (Windows) or ifconfig (Mac/Linux)
      const response = await fetch('http://********:3001/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: signUpUsername,
          email: signUpEmail,
          password: signUpPassword,
          avatar: selectedAvatar || 'default image'
        })
      });

      const data = await response.json();

      if (data.success) {
        setIsLoading(false);
        Alert.alert(
          'Welcome to Riddle Quest!',
          `🎉 Welcome to Riddle Quest, ${signUpUsername}!\n\nYour account has been created successfully!`
        );

        // Clear form
        setSignUpUsername('');
        setSignUpEmail('');
        setSignUpPassword('');
        setConfirmPassword('');
        setSelectedAvatar(null);
        setAvatarType('placeholder');

        // Optionally switch to sign in tab
        setActiveTab('signin');

      } else {
        setIsLoading(false);
        // Handle specific error messages from backend
        const updatedErrors = { ...newErrors };
        if (data.message.includes('email')) {
          updatedErrors.email = data.message;
        } else if (data.message.includes('username')) {
          updatedErrors.username = data.message;
        } else if (data.message.includes('password')) {
          updatedErrors.password = data.message;
        } else {
          updatedErrors.email = data.message;
        }
        setSignUpErrors(updatedErrors);
      }

    } catch (error) {
      setIsLoading(false);
      console.error('Registration error:', error);
      Alert.alert(
        'Network Error',
        '❌ Network error. Please check if the server is running and try again.\n\nMake sure your backend server is running on port 3001.'
      );
    }
  };

  // Interpolate floating animations
  const float1Y = floatAnim1.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -20],
  });
  const float1Rotate = floatAnim1.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });
  const float1Opacity = floatAnim1.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.7, 1, 0.7],
  });

  const float2Y = floatAnim2.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -20],
  });
  const float2Rotate = floatAnim2.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });
  const float2Opacity = floatAnim2.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.7, 1, 0.7],
  });

  const float3Y = floatAnim3.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -20],
  });
  const float3Rotate = floatAnim3.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });
  const float3Opacity = floatAnim3.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.7, 1, 0.7],
  });

  return (
    <ScrollView>
    <View style={styles.container}>
      {/* Animated background elements */}
      <Animated.View 
        style={[
          styles.floatingShape, 
          styles.shape1,
          {
            transform: [{ translateY: float1Y }, { rotate: float1Rotate }],
            opacity: float1Opacity,
          }
        ]}
      />
      <Animated.View 
        style={[
          styles.floatingShape, 
          styles.shape2,
          {
            transform: [{ translateY: float2Y }, { rotate: float2Rotate }],
            opacity: float2Opacity,
          }
        ]}
      />
      <Animated.View 
        style={[
          styles.floatingShape, 
          styles.shape3,
          {
            transform: [{ translateY: float3Y }, { rotate: float3Rotate }],
            opacity: float3Opacity,
          }
        ]}
      />

      {/* Main content */}
      <View style={styles.formContainer}>
        {/* Logo */}
        <View style={styles.logo}>
          <Text style={styles.logoTitle}>🎭 Riddle Quest</Text>
          <Text style={styles.logoSubtitle}>Truth, Dare & Mystery Await</Text>
        </View>

        {/* Tab Buttons */}
        <View style={styles.tabButtons}>
          <TouchableOpacity
            style={[
              styles.tabBtn,
              activeTab === 'signin' && styles.tabBtnActive
            ]}
            onPress={() => setActiveTab('signin')}
          >
            <Text style={[
              styles.tabBtnText,
              activeTab === 'signin' && styles.tabBtnTextActive
            ]}>
              Sign In
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.tabBtn,
              activeTab === 'signup' && styles.tabBtnActive
            ]}
            onPress={() => setActiveTab('signup')}
          >
            
            <Text style={[
              styles.tabBtnText,
              activeTab === 'signup' && styles.tabBtnTextActive
            ]}>
              Sign Up
            </Text>
          </TouchableOpacity>
        </View>

        {/* Sign In Form */}
        {activeTab === 'signin' && (
          <View style={styles.formContent}>
            <TouchableOpacity
              style={styles.googleBtn}
              onPress={signInWithGoogle}
            >
              <Text>📧</Text>
              <Text style={styles.googleBtnText}>Continue with Google</Text>
            </TouchableOpacity>

            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>or</Text>
              <View style={styles.dividerLine} />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Email Address</Text>
              <TextInput
                style={[
                  styles.input,
                  signInErrors.email && styles.inputError
                ]}
                value={signInEmail}
                onChangeText={setSignInEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                placeholder="Enter your email"
                placeholderTextColor="#78909C"
              />
              {signInErrors.email ? (
                <Text style={styles.errorMessage}>{signInErrors.email}</Text>
              ) : null}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Password</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={[
                    styles.input,
                    signInErrors.password && styles.inputError
                  ]}
                  value={signInPassword}
                  onChangeText={setSignInPassword}
                  secureTextEntry={!showSignInPassword}
                  placeholder="Enter your password"
                  placeholderTextColor="#78909C"
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowSignInPassword(!showSignInPassword)}
                >
                  <Text>{showSignInPassword ? '🙈' : '👁️'}</Text>
                </TouchableOpacity>
              </View>
              {signInErrors.password ? (
                <Text style={styles.errorMessage}>{signInErrors.password}</Text>
              ) : null}
            </View>

            
<TouchableOpacity
  style={[styles.submitBtn, isLoading && styles.loadingBtn]}
  onPress={handleSignIn}
  disabled={isLoading}
  activeOpacity={0.9}
>
  <LinearGradient
    colors={['#64ffda', '#00bcd4']}
    start={{ x: 0, y: 0 }}
    end={{ x: 1, y: 1 }}
    style={{ ...StyleSheet.absoluteFillObject, borderRadius: 12 }}
  />
  {isLoading ? (
    <View style={styles.loadingIndicator} />
  ) : (
    <Text style={styles.submitBtnText}>Enter the Game</Text>
  )}
</TouchableOpacity>
          </View>
        )}

        {/* Sign Up Form */}
        {activeTab === 'signup' && (
          <View style={styles.formContent}>
            <TouchableOpacity
              style={styles.googleBtn}
              onPress={signUpWithGoogle}
            >
              <Text>📧</Text>
              <Text style={styles.googleBtnText}>Sign up with Google</Text>
            </TouchableOpacity>

            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>or</Text>
              <View style={styles.dividerLine} />
            </View>

            {/* Avatar Section */}
            <View style={styles.avatarSection}>
              <View style={styles.avatarPreview}>
                {avatarType === 'image' && selectedAvatar ? (
                  <Image
                    source={{ uri: selectedAvatar }}
                    style={styles.avatarImage}
                  />
                ) : avatarType === 'emoji' && selectedAvatar ? (
                  <Text style={styles.avatarEmoji}>{selectedAvatar}</Text>
                ) : (
                  <Text style={styles.avatarPlaceholder}>👤</Text>
                )}
              </View>

              <View style={styles.avatarOptions}>
                <TouchableOpacity
                  style={styles.avatarBtn}
                  onPress={handleImageUpload}
                >
                  <Text style={styles.avatarBtnText}>Upload Photo</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.avatarBtn}
                  onPress={() => setShowPresetAvatars(true)}
                >
                  <Text style={styles.avatarBtnText}>Choose Avatar</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Username</Text>
              <TextInput
                style={[
                  styles.input,
                  signUpErrors.username && styles.inputError
                ]}
                value={signUpUsername}
                onChangeText={setSignUpUsername}
                autoCapitalize="none"
                placeholder="Choose a username"
                placeholderTextColor="#78909C"
              />
              {signUpErrors.username ? (
                <Text style={styles.errorMessage}>{signUpErrors.username}</Text>
              ) : null}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Email Address</Text>
              <TextInput
                style={[
                  styles.input,
                  signUpErrors.email && styles.inputError
                ]}
                value={signUpEmail}
                onChangeText={setSignUpEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                placeholder="Enter your email"
                placeholderTextColor="#78909C"
              />
              {signUpErrors.email ? (
                <Text style={styles.errorMessage}>{signUpErrors.email}</Text>
              ) : null}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Password</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={[
                    styles.input,
                    signUpErrors.password && styles.inputError
                  ]}
                  value={signUpPassword}
                  onChangeText={(text) => {
                    setSignUpPassword(text);
                    checkPasswordStrength(text);
                  }}
                  secureTextEntry={!showSignUpPassword}
                  placeholder="Create a password"
                  placeholderTextColor="#78909C"
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowSignUpPassword(!showSignUpPassword)}
                >
                  <Text>{showSignUpPassword ? '🙈' : '👁️'}</Text>
                </TouchableOpacity>
              </View>
              {passwordStrength ? (
                <Text style={[
                  styles.passwordStrength,
                  passwordStrength.includes('Weak') ? styles.strengthWeak :
                  passwordStrength.includes('Medium') ? styles.strengthMedium :
                  styles.strengthStrong
                ]}>
                  {passwordStrength}
                </Text>
              ) : null}
              {signUpErrors.password ? (
                <Text style={styles.errorMessage}>{signUpErrors.password}</Text>
              ) : null}
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.label}>Confirm Password</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={[
                    styles.input,
                    signUpErrors.confirmPassword && styles.inputError
                  ]}
                  value={confirmPassword}
                  onChangeText={(text) => {
                    setConfirmPassword(text);
                    checkPasswordMatch();
                  }}
                  secureTextEntry={!showConfirmPassword}
                  placeholder="Confirm your password"
                  placeholderTextColor="#78909C"
                />
                <TouchableOpacity
                  style={styles.passwordToggle}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  <Text>{showConfirmPassword ? '🙈' : '👁️'}</Text>
                </TouchableOpacity>
              </View>
              {signUpErrors.confirmPassword ? (
                <Text style={styles.errorMessage}>{signUpErrors.confirmPassword}</Text>
              ) : null}
            </View>

            <TouchableOpacity
              style={[
                styles.submitBtn,
                isLoading && styles.loadingBtn
              ]}
              onPress={handleSignUp}
              disabled={isLoading}
            >
                <LinearGradient
    colors={['#64ffda', '#00bcd4']}
    start={{ x: 0, y: 0 }}
    end={{ x: 1, y: 1 }}
    style={{ ...StyleSheet.absoluteFillObject, borderRadius: 12 }}
  />
              {isLoading ? (
                <View style={styles.loadingIndicator} />
              ) : (
                <Text style={styles.submitBtnText}>Join the Adventure</Text>
              )}
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Preset Avatars Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={showPresetAvatars}
        onRequestClose={() => setShowPresetAvatars(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Choose an Avatar</Text>
            <View style={styles.presetAvatarsContainer}>
              {presetAvatars.map((avatar, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.presetAvatar,
                    { backgroundColor: avatar.color },
                    selectedAvatar === avatar.emoji && styles.presetAvatarSelected
                  ]}
                  onPress={() => selectPresetAvatar(avatar.emoji)}
                >
                  <Text style={styles.presetAvatarEmoji}>{avatar.emoji}</Text>
                </TouchableOpacity>
              ))}
            </View>
            <TouchableOpacity
              style={styles.modalCancelBtn}
              onPress={() => setShowPresetAvatars(false)}
            >
              <Text style={styles.modalCancelBtnText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    overflow: 'hidden',
  },
  floatingShape: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 50,
  },
  shape1: {
    width: 80,
    height: 80,
    top: '20%',
    left: '10%',
  },
  shape2: {
    width: 60,
    height: 60,
    top: '60%',
    right: '15%',
  },
  shape3: {
    width: 100,
    height: 100,
    bottom: '20%',
    left: '20%',
  },
  formContainer: {
    backgroundColor: 'rgba(20, 20, 35, 0.95)',
    borderRadius: 20,
    padding: 40,
    width: '90%',
    maxWidth: 450,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 15 },
    shadowOpacity: 0.5,
    shadowRadius: 35,
    elevation: 10,
  },
  logo: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logoTitle: {
    color: '#64ffda',
    fontSize: 30,
    fontWeight: 'bold',
    textShadowColor: 'rgba(100, 255, 218, 0.3)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 20,
    marginBottom: 10,
  },
  logoSubtitle: {
    color: '#b0bec5',
    fontSize: 16,
    fontStyle: 'italic',
  },
  tabButtons: {
    flexDirection: 'row',
    marginBottom: 30,
    backgroundColor: '#2a2a3e',
    borderRadius: 12,
    padding: 5,
  },
  tabBtn: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  tabBtnActive: {
    backgroundColor: '#64ffda',
    shadowColor: '#64ffda',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 5,
  },
  tabBtnText: {
    color: '#90a4ae',
    fontWeight: '600',
  },
  tabBtnTextActive: {
    color: '#1a1a2e',
  },
  formContent: {
    width: '100%',
  },
  googleBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 14,
    borderWidth: 2,
    borderColor: '#ea4335',
    borderRadius: 12,
    marginBottom: 20,
  },
  googleBtnText: {
    color: '#ea4335',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 10,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: '#455a64',
  },
  dividerText: {
    color: '#78909c',
    paddingHorizontal: 15,
    backgroundColor: 'rgba(20, 20, 35, 0.95)',
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    color: '#eceff1',
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    width: '100%',
    padding: 14,
    borderWidth: 2,
    borderColor: '#455a64',
    borderRadius: 12,
    fontSize: 16,
    backgroundColor: '#263238',
    color: '#eceff1',
  },
  inputError: {
    borderColor: '#ff5252',
    shadowColor: '#ff5252',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  passwordToggle: {
    position: 'absolute',
    right: 16,
    padding: 4,
    borderRadius: 4,
  },
  avatarSection: {
    alignItems: 'center',
    marginBottom: 20,
  },
  avatarPreview: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#2a2a3e',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: '#455a64',
    marginBottom: 15,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 50,
  },
  avatarEmoji: {
    fontSize: 40,
  },
  avatarPlaceholder: {
    fontSize: 40,
    color: '#90a4ae',
  },
  avatarOptions: {
    flexDirection: 'row',
    gap: 10,
    marginBottom: 15,
  },
  avatarBtn: {
    padding: 8,
    borderWidth: 2,
    borderColor: '#64ffda',
    borderRadius: 8,
  },
  avatarBtnText: {
    color: '#64ffda',
    fontSize: 14,
  },
  passwordStrength: {
    marginTop: 8,
    padding: 8,
    borderRadius: 6,
    fontSize: 12,
  },
  strengthWeak: {
    backgroundColor: 'rgba(255, 82, 82, 0.1)',
    color: '#ff5252',
    borderWidth: 1,
    borderColor: 'rgba(255, 82, 82, 0.3)',
  },
  strengthMedium: {
    backgroundColor: 'rgba(255, 193, 7, 0.1)',
    color: '#ffc107',
    borderWidth: 1,
    borderColor: 'rgba(255, 193, 7, 0.3)',
  },
  strengthStrong: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    color: '#4caf50',
    borderWidth: 1,
    borderColor: 'rgba(76, 175, 80, 0.3)',
  },
  submitBtn: {
    width: '100%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#64ffda',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 5,
    backgroundColor: 'linear-gradient(135deg, #64ffda, #00bcd4)',
  },
  loadingBtn: {
    opacity: 0.7,
  },
  submitBtnText: {
    color: '#1a1a2e',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#64ffda',
    borderTopColor: 'transparent',
  },
  errorMessage: {
    color: '#ff5252',
    fontSize: 14,
    marginTop: 5,
    padding: 8,
    backgroundColor: 'rgba(255, 82, 82, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 82, 82, 0.3)',
    borderRadius: 8,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  modalContent: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    maxWidth: 350,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  presetAvatarsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  presetAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 10,
  },
  presetAvatarSelected: {
    borderWidth: 3,
    borderColor: '#64ffda',
    shadowColor: '#64ffda',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 5,
  },
  presetAvatarEmoji: {
    fontSize: 30,
  },
  modalCancelBtn: {
    marginTop: 20,
    padding: 10,
    alignSelf: 'center',
  },
  modalCancelBtnText: {
    color: 'blue',
  },
  // Gradient background for active tab and submit button
  gradient: {
    flex: 1,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default RiddleQuestAuth;