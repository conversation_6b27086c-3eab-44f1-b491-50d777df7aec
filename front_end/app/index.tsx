import React from 'react';
import { View, Text, StyleSheet,ScrollView, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import FloatingOrb from './IndexComponents/FloatingOrb'// it's in a separate file
import Sparkle from './IndexComponents/sparkle'; // You can extract the sparkle component too
import LogoSection from './IndexComponents/logoo';
import GameShowcase from './IndexComponents/gamecard';
import  CTASection  from './IndexComponents/cta'

const { width, height } = Dimensions.get('window');

export default function WelcomeScreen() {
  return (
<ScrollView contentContainerStyle={{ flexGrow: 1 }} >
    <LinearGradient
      colors={['#0a0a23', '#1a1a3e', '#2d1b69', '#4a148c']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.screenContainer}
    >
      {/* ✨ Sparkles on background */}
      <Sparkle style={{ top: 40, left: 30 }} />
      <Sparkle style={{ bottom: 100, right: 60 }} />
      <Sparkle style={{ top: 200, right: 120 }} />
      <Sparkle style={{ bottom: 40, left: 20 }} />

      {/* 🌌 Floating glowing orbs */}
      <View style={styles.bgEffects}>
        <FloatingOrb size={120} style={{ top: '10%', left: '10%' }} delay={0} />
        <FloatingOrb size={80} style={{ top: '60%', right: '15%' }} delay={2000} />
        <FloatingOrb size={100} style={{ bottom: '20%', left: '20%' }} delay={4000} />
        <FloatingOrb size={60} style={{ top: '30%', right: '30%' }} delay={6000} />
      </View>

      {/* 🎭 UI Content */}
      <View style={styles.container}>
        <LogoSection />

       <GameShowcase/>

        <CTASection/>
      </View>
    </LinearGradient>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  bgEffects: {
    position: 'absolute',
    width,
    height,
    zIndex: 0,
  },

  container: {
    position: 'relative',
    zIndex: 1,
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
});
