import React from 'react';
import './landingstyle.css'; // We'll move the CSS to a separate file
import AnimatedBackground from './animateBackground';

export function IndexPage() {


  return (
    <>
    <AnimatedBackground />

      <div className="container">
      
        <div className="logo-section">
            <h1 className="main-logo">🎭 Riddle Quest</h1>
            <p className="subtitle">Truth, Dare & Mystery Await</p>
            <p className="tagline">Challenge Your Mind, Test Your Courage</p>
        </div>

      
        <div className="game-showcase">
            <div className="game-card truth-card will-change">
                <div className="card-icon">🔍</div>
                <h3 className="card-title">Truth</h3>
                <p className="card-description">Reveal secrets and discover hidden truths about yourself and friends</p>
            </div>
            
            <div className="game-card dare-card will-change">
                <div className="card-icon">🎯</div>
                <h3 className="card-title">Dare</h3>
                <p className="card-description">Accept thrilling challenges that push your limits and create memories</p>
            </div>
            
            <div className="game-card riddle-card will-change">
                <div className="card-icon">🧩</div>
                <h3 className="card-title">Riddle</h3>
                <p className="card-description">Solve mind-bending puzzles and unlock the mysteries of the quest</p>
            </div>
        </div>

      
        <div className="cta-section">
            <p className="cta-text">Ready for the Ultimate Adventure?</p>
            <button className="enter-btn will-change" onclick="enterGame()">
                Enter the Quest
            </button>
        </div>
    </div>

    
    <div className="loading-overlay" id="loadingOverlay">
        <div style={{textAlign:'center'}}>
            <div className="loading-spinner"></div>
            <p className="loading-text">Preparing Your Adventure...</p>
        </div>
    </div>


    </>
  )
  
}