import React, { useEffect, useState } from 'react';

const AnimatedBackground = () => {
  const [sparkles, setSparkles] = useState([]);

  useEffect(() => {
    // Create initial sparkles
    //const initialSparkles = [];
    for (let i = 0; i < 10; i++) {
      setTimeout(() => {
        createSparkle();
      }, i * 100);
    }

    // Create sparkles periodically
    const interval = setInterval(() => {
      createSparkle();
    }, 500);

    return () => clearInterval(interval);
  }, []);

  const createSparkle = () => {
    const id = Math.random().toString(36).substr(2, 9);
    const newSparkle = {
      id,
      left: Math.random() * 100,
      top: Math.random() * 100,
      animationDelay: Math.random() * 3
    };

    setSparkles(prev => [...prev, newSparkle]);

    // Remove sparkle after 3 seconds
    setTimeout(() => {
      setSparkles(prev => prev.filter(sparkle => sparkle.id !== id));
    }, 3000);
  };

  return (
    <div className="fixed top-0 left-0 w-full h-full pointer-events-none z-0">
      {/* Floating Orbs */}
      <div 
        className="absolute rounded-full opacity-60"
        style={{
          width: '120px',
          height: '120px',
          top: '10%',
          left: '10%',
          background: 'radial-gradient(circle, rgba(100, 255, 218, 0.3), rgba(100, 255, 218, 0.1))',
          filter: 'blur(1px)',
          animation: 'float 8s ease-in-out infinite 0s'
        }}
      />
      
      <div 
        className="absolute rounded-full opacity-60"
        style={{
          width: '80px',
          height: '80px',
          top: '60%',
          right: '15%',
          background: 'radial-gradient(circle, rgba(100, 255, 218, 0.3), rgba(100, 255, 218, 0.1))',
          filter: 'blur(1px)',
          animation: 'float 8s ease-in-out infinite 2s'
        }}
      />
      
      <div 
        className="absolute rounded-full opacity-60"
        style={{
          width: '100px',
          height: '100px',
          bottom: '20%',
          left: '20%',
          background: 'radial-gradient(circle, rgba(100, 255, 218, 0.3), rgba(100, 255, 218, 0.1))',
          filter: 'blur(1px)',
          animation: 'float 8s ease-in-out infinite 4s'
        }}
      />
      
      <div 
        className="absolute rounded-full opacity-60"
        style={{
          width: '60px',
          height: '60px',
          top: '30%',
          right: '30%',
          background: 'radial-gradient(circle, rgba(100, 255, 218, 0.3), rgba(100, 255, 218, 0.1))',
          filter: 'blur(1px)',
          animation: 'float 8s ease-in-out infinite 6s'
        }}
      />

      {/* Sparkles */}
      {sparkles.map(sparkle => (
        <div
          key={sparkle.id}
          className="absolute w-1 h-1 rounded-full"
          style={{
            left: `${sparkle.left}%`,
            top: `${sparkle.top}%`,
            background: '#64ffda',
            animation: `sparkle 3s linear infinite ${sparkle.animationDelay}s`
          }}
        />
      ))}

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) translateX(0px) rotate(0deg);
            opacity: 0.6;
          }
          25% {
            transform: translateY(-20px) translateX(10px) rotate(90deg);
            opacity: 0.8;
          }
          50% {
            transform: translateY(-10px) translateX(-15px) rotate(180deg);
            opacity: 1;
          }
          75% {
            transform: translateY(15px) translateX(5px) rotate(270deg);
            opacity: 0.7;
          }
        }

        @keyframes sparkle {
          0% {
            opacity: 0;
            transform: scale(0);
          }
          50% {
            opacity: 1;
            transform: scale(1);
          }
          100% {
            opacity: 0;
            transform: scale(0);
          }
        }
      `}</style>
    </div>
  );
};

export default AnimatedBackground;