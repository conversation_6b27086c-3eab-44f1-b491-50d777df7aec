
        /* Main Container */
        .container {
            position: relative;
            z-index: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            text-align: center;
        }

        /* Logo and Title */
        .logo-section {
            margin-bottom: 40px;
            animation: slideUp 1.5s ease-out;
        }

        .main-logo {
            font-size: clamp(3rem, 8vw, 5rem);
            font-weight: bold;
            background: linear-gradient(45deg, #64ffda, #ff6b9d, #c471ed, #12c2e9);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 4s ease-in-out infinite, textGlow 2s ease-in-out infinite alternate;
            margin-bottom: 15px;
            text-shadow: 0 0 30px rgba(100, 255, 218, 0.3);
        }

        .subtitle {
            font-size: clamp(1.2rem, 4vw, 1.8rem);
            color: #b0bec5;
            font-style: italic;
            margin-bottom: 10px;
            animation: fadeIn 2s ease-out 0.5s both;
        }

        .tagline {
            font-size: clamp(0.9rem, 3vw, 1.2rem);
            color: #78909c;
            animation: fadeIn 2s ease-out 1s both;
        }

        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        @keyframes textGlow {
            0% {
                filter: drop-shadow(0 0 10px rgba(100, 255, 218, 0.5));
            }
            100% {
                filter: drop-shadow(0 0 20px rgba(100, 255, 218, 0.8));
            }
        }

        /* Animated Game Elements */
        .game-showcase {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 40px;
            margin: 60px 0;
            flex-wrap: wrap;
        }

        .game-card {
            background: rgba(20, 20, 35, 0.8);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px 25px;
            width: 200px;
            height: 250px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 2px solid rgba(100, 255, 218, 0.2);
            transition: all 0.4s ease;
            animation: cardFloat 6s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }

        .game-card:nth-child(1) {
            animation-delay: 0s;
        }

        .game-card:nth-child(2) {
            animation-delay: 2s;
        }

        .game-card:nth-child(3) {
            animation-delay: 4s;
        }

        .game-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(100, 255, 218, 0.1), transparent);
            animation: rotate 4s linear infinite;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .game-card:hover::before {
            opacity: 1;
        }

        .game-card:hover {
            transform: translateY(-10px) scale(1.05);
            border-color: #64ffda;
            box-shadow: 0 20px 40px rgba(100, 255, 218, 0.3);
        }

        @keyframes cardFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-15px);
            }
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .card-icon {
            font-size: 4rem;
            margin-bottom: 15px;
            animation: iconPulse 3s ease-in-out infinite;
        }

        .truth-card .card-icon {
            animation-delay: 0s;
        }

        .dare-card .card-icon {
            animation-delay: 1s;
        }

        .riddle-card .card-icon {
            animation-delay: 2s;
        }

        @keyframes iconPulse {
            0%, 100% {
                transform: scale(1);
                filter: brightness(1);
            }
            50% {
                transform: scale(1.1);
                filter: brightness(1.3);
            }
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #64ffda;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .card-description {
            font-size: 0.9rem;
            color: #b0bec5;
            line-height: 1.4;
            text-align: center;
        }

        /* Animated Call-to-Action */
        .cta-section {
            margin-top: 60px;
            animation: slideUp 2s ease-out 1.5s both;
        }

        .cta-text {
            font-size: clamp(1.2rem, 4vw, 1.6rem);
            color: #eceff1;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .enter-btn {
            background: linear-gradient(135deg, #64ffda, #00bcd4, #ff6b9d);
            background-size: 300% 300%;
            color: #1a1a2e;
            border: none;
            border-radius: 50px;
            padding: 18px 40px;
            font-size: clamp(1.1rem, 3vw, 1.3rem);
            font-weight: bold;
            cursor: pointer;
            transition: all 0.4s ease;
            animation: gradientShift 3s ease-in-out infinite, btnFloat 4s ease-in-out infinite;
            box-shadow: 0 10px 30px rgba(100, 255, 218, 0.4);
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .enter-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transition: all 0.4s ease;
            transform: translate(-50%, -50%);
        }

        .enter-btn:hover::before {
            width: 100%;
            height: 100%;
        }

        .enter-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 40px rgba(100, 255, 218, 0.6);
        }

        .enter-btn:active {
            transform: translateY(-1px) scale(1.02);
        }

        @keyframes btnFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-5px);
            }
        }

        /* Loading Animation */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0a0a23, #1a1a3e);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.5s ease;
        }

        .loading-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            width: 80px;
            height: 80px;
            border: 4px solid rgba(100, 255, 218, 0.3);
            border-top: 4px solid #64ffda;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: #64ffda;
            font-size: 1.2rem;
            margin-top: 20px;
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 0.7;
            }
            50% {
                opacity: 1;
            }
        }

        /* Animations */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* Mobile Optimizations */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .logo-section {
                margin-bottom: 30px;
            }
            
            .game-showcase {
                gap: 20px;
                margin: 40px 0;
            }
            
            .game-card {
                width: 160px;
                height: 200px;
                padding: 20px 15px;
            }
            
            .card-icon {
                font-size: 3rem;
                margin-bottom: 10px;
            }
            
            .card-title {
                font-size: 1.2rem;
                margin-bottom: 8px;
            }
            
            .card-description {
                font-size: 0.8rem;
            }
            
            .cta-section {
                margin-top: 40px;
            }
            
            .enter-btn {
                padding: 15px 30px;
                width: 100%;
                max-width: 300px;
            }
        }

        @media (max-width: 480px) {
            .game-showcase {
                flex-direction: column;
                gap: 15px;
            }
            
            .game-card {
                width: 100%;
                max-width: 250px;
                height: 180px;
            }
        }

        /* Performance Optimizations */
        .will-change {
            will-change: transform, opacity;
        }