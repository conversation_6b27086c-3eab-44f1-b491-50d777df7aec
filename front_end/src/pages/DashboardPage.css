
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
    color: #ffffff;
    min-height: 100vh;
    overflow-x: hidden;
}

.container-dashboard {
    max-width: 100%;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px 0;
}

.logo {
    font-size: 2.5em;
    font-weight: bold;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 10px;
}

.subtitle {
    color: #b8b8b8;
    font-size: 1.1em;
}

.user-profile {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.15), rgba(78, 205, 196, 0.15));
    backdrop-filter: blur(15px);
    border-radius: 25px;
    padding: 30px;
    margin-bottom: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 15px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.15);
    position: relative;
    overflow: hidden;
}

.user-profile::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.profile-pic {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5em;
    font-weight: bold;
    flex-shrink: 0;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 1;
}

.user-info {
    flex: 1;
    position: relative;
    z-index: 1;
}

.username {
    font-size: 1.8em;
    font-weight: bold;
    margin-bottom: 8px;
    background: linear-gradient(45deg, #ffffff, #f0f0f0);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.level-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.level {
    color: #4ecdc4;
    font-weight: 600;
    font-size: 1.1em;
}

.nickname {
    color: #ff6b6b;
    font-size: 1.2em;
    font-weight: 600;
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 0 auto;
    max-width: 900px;
    margin-bottom: 30px;
}

.menu-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.menu-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    background: rgba(255, 255, 255, 0.15);
}

.menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.menu-item:hover::before {
    left: 100%;
}

.menu-icon {
    font-size: 3em;
    margin-bottom: 15px;
}

.solo { color: #ff6b6b; }
.multiplayer { color: #4ecdc4; }
.truth-dare { color: #45b7d1; }
.riddle-dare { color: #f39c12; }
.extras { color: #9b59b6; }

.coming-soon {
    position: relative;
    opacity: 0.8;
}

.coming-soon-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, #ff6b6b, #e74c3c);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.coming-soon:hover {
    opacity: 0.9;
    cursor: not-allowed;
}

.menu-title {
    font-size: 1.3em;
    font-weight: bold;
    margin-bottom: 10px;
}

.menu-desc {
    color: #b8b8b8;
    font-size: 0.95em;
    line-height: 1.4;
}

.extras-menu {
    display: none;
    animation: slideIn 0.3s ease-out;
}

.extras-menu.active {
    display: block;
}

.back-btn {
    background: rgba(255, 107, 107, 0.2);
    border: 2px solid #ff6b6b;
    color: #ff6b6b;
    border-radius: 25px;
    padding: 12px 25px;
    font-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 25px;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.back-btn:hover {
    background: #ff6b6b;
    color: white;
    transform: translateX(-5px);
}

.extras-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.extras-item {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.extras-item:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.extras-icon {
    font-size: 2.5em;
    margin-bottom: 12px;
}

.progress { color: #3498db; }
.leaderboard { color: #f1c40f; }
.settings { color: #95a5a6; }
.help { color: #2ecc71; }
.logout { color: #e74c3c; }

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@media (max-width: 768px) {
    .container-dashboard {
        padding: 15px;
    }

    .logo {
        font-size: 2em;
    }

    .user-profile {
        padding: 25px;
    }

    .profile-pic {
        width: 85px;
        height: 85px;
        font-size: 2.2em;
    }

    .username {
        font-size: 1.5em;
    }

    .menu-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        max-width: 400px;
    }

    .menu-item {
        padding: 20px;
    }

    .menu-icon {
        font-size: 2.5em;
    }

    .menu-title {
        font-size: 1.2em;
    }

    .extras-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .logo {
        font-size: 1.8em;
    }

    .user-profile {
        padding: 20px;
    }

    .profile-pic {
        width: 75px;
        height: 75px;
        font-size: 2em;
    }

    .menu-item {
        padding: 18px;
    }

    .menu-grid {
        gap: 12px;
    }

    .extras-grid {
        grid-template-columns: 1fr;
    }
}
