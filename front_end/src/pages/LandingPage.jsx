
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './LandingPage.css';

const LandingPage = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const createSparkle = () => {
      const sparkle = document.createElement('div');
      sparkle.className = 'sparkle';
      sparkle.style.left = `${Math.random() * 100}%`;
      sparkle.style.top = `${Math.random() * 100}%`;
      sparkle.style.animationDelay = `${Math.random() * 3}s`;
      
      const bgEffects = document.querySelector('.bg-effects');
      if (bgEffects) {
        bgEffects.appendChild(sparkle);
        setTimeout(() => {
          sparkle.remove();
        }, 3000);
      }
    };

    const intervalId = setInterval(createSparkle, 500);
    for (let i = 0; i < 10; i++) {
      setTimeout(createSparkle, i * 100);
    }

    return () => clearInterval(intervalId);
  }, []);

  const enterGame = () => {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
      loadingOverlay.classList.add('active');
      setTimeout(() => {
        navigate('/signup');
      }, 2000);
    }
  };

  return (
    <>
      <div className="bg-effects">
        <div className="floating-orb orb1"></div>
        <div className="floating-orb orb2"></div>
        <div className="floating-orb orb3"></div>
        <div className="floating-orb orb4"></div>
      </div>

      <div className="container">
        <div className="logo-section">
          <h1 className="main-logo">🎭 Riddle Quest</h1>
          <p className="subtitle">Truth, Dare & Mystery Await</p>
          <p className="tagline">Challenge Your Mind, Test Your Courage</p>
        </div>

        <div className="game-showcase">
          <div className="game-card truth-card will-change">
            <div className="card-icon">🔍</div>
            <h3 className="card-title">Truth</h3>
            <p className="card-description">Reveal secrets and discover hidden truths about yourself and friends</p>
          </div>
          
          <div className="game-card dare-card will-change">
            <div className="card-icon">🎯</div>
            <h3 className="card-title">Dare</h3>
            <p className="card-description">Accept thrilling challenges that push your limits and create memories</p>
          </div>
          
          <div className="game-card riddle-card will-change">
            <div className="card-icon">🧩</div>
            <h3 className="card-title">Riddle</h3>
            <p className="card-description">Solve mind-bending puzzles and unlock the mysteries of the quest</p>
          </div>
        </div>

        <div className="cta-section">
          <p className="cta-text">Ready for the Ultimate Adventure?</p>
          <button className="enter-btn will-change" onClick={enterGame}>
            Enter the Quest
          </button>
        </div>
      </div>

      <div className="loading-overlay" id="loadingOverlay">
        <div style={{ textAlign: 'center' }}>
          <div className="loading-spinner"></div>
          <p className="loading-text">Preparing Your Adventure...</p>
        </div>
      </div>
    </>
  );
};

export default LandingPage;
