
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import './VerificationCodePage.css';

const VerificationCodePage = () => {
  const navigate = useNavigate();
  const [code, setCode] = useState('');
  const [error, setError] = useState('');

  const handleVerify = (e) => {
    e.preventDefault();
    if (code === '1234') {
      navigate('/dashboard');
    } else {
      setError('Invalid verification code');
    }
  };

  return (
    <div className="container-verification">
      <div className="logo">
        <h1>🎭 Riddle Quest</h1>
        <p>Truth, Dare & Mystery Await</p>
      </div>
      <h2>Enter Verification Code</h2>
      <p>A verification code has been sent to your email.</p>
      <form onSubmit={handleVerify}>
        <div className="form-group">
          <label htmlFor="verification-code">Verification Code</label>
          <input type="text" id="verification-code" value={code} onChange={(e) => setCode(e.target.value)} required />
        </div>
        {error && <div className="error-message">{error}</div>}
        <button type="submit" className="submit-btn">Verify</button>
      </form>
    </div>
  );
};

export default VerificationCodePage;
