
import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import './SignUpPage.css';

const SignUpPage = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('signup');

  const switchTab = (tab) => {
    setActiveTab(tab);
  };

  return (
    <div className="container-signup">
        <div className="bg-decoration">
        <div className="floating-shape shape1"></div>
        <div className="floating-shape shape2"></div>
        <div className="floating-shape shape3"></div>
    </div>
      <div className="logo">
        <h1>🎭 Riddle Quest</h1>
        <p>Truth, Dare & Mystery Await</p>
      </div>

      <div className="tab-buttons">
        <button className={`tab-btn ${activeTab === 'signin' ? 'active' : ''}`} onClick={() => switchTab('signin')}>Sign In</button>
        <button className={`tab-btn ${activeTab === 'signup' ? 'active' : ''}`} onClick={() => switchTab('signup')}>Sign Up</button>
      </div>

      {activeTab === 'signin' ? <SignInForm /> : <SignUpForm />}
    </div>
  );
};

const SignInForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSignIn = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      const response = await fetch('http://localhost:3000/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      const data = await response.json();

      if (data.success) {
        // Handle successful login
        alert(`Welcome back, ${data.user.username}!`);
        // You can navigate to dashboard or store user data
      } else {
        setError(data.message);
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="form-container active">
      <button className="google-btn">
        <span>📧</span>
        Continue with Google
      </button>
      <div className="divider"><span>or</span></div>
      <form onSubmit={handleSignIn}>
        <div className="form-group">
          <label htmlFor="signin-email">Email Address</label>
          <input
            type="email"
            id="signin-email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email"
            required
          />
        </div>
        <div className="form-group">
          <label htmlFor="signin-password">Password</label>
          <div className="password-container">
            <input
              type={showPassword ? "text" : "password"}
              id="signin-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              required
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? '🙈' : '👁️'}
            </button>
          </div>
        </div>
        {error && <div className="error-message">{error}</div>}
        <button type="submit" className="submit-btn" disabled={isLoading}>
          {isLoading ? 'Signing In...' : 'Enter the Game'}
        </button>
      </form>
    </div>
  );
};

const SignUpForm = () => {
  const navigate = useNavigate();
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [avatar, setAvatar] = useState(null);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const fileInputRef = useRef(null);

  const handleSignUp = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    // Basic validation
    if (!username || !email || !password || !confirmPassword) {
      setError('All fields are required');
      setIsLoading(false);
      return;
    }

    if (username.length < 3) {
      setError('Username must be at least 3 characters long');
      setIsLoading(false);
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      setIsLoading(false);
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('http://localhost:3000/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, email, password, avatar })
      });

      const data = await response.json();

      if (data.success) {
        alert(`Welcome to Riddle Quest, ${username}! Your account has been created successfully!`);
        // Clear form
        setUsername('');
        setEmail('');
        setPassword('');
        setConfirmPassword('');
        setAvatar(null);
        // You can navigate to a different page or show success message
        // navigate('/dashboard');
      } else {
        setError(data.message);
      }
    } catch (error) {
      setError('Network error. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="form-container active">
      <button className="google-btn">
        <span>📧</span>
        Sign up with Google
      </button>
      <div className="divider"><span>or</span></div>
      <form onSubmit={handleSignUp}>
        <div className="avatar-section">
          <div className="avatar-preview">
            {avatar ? <img src={avatar} alt="Avatar" /> : <span className="avatar-placeholder">👤</span>}
          </div>
          <div className="avatar-options">
            <button type="button" className="avatar-btn" onClick={() => fileInputRef.current.click()}>Upload Photo</button>
            <button type="button" className="avatar-btn">Choose Avatar</button>
          </div>
          <input
            type="file"
            ref={fileInputRef}
            style={{ display: 'none' }}
            accept="image/*"
            onChange={(e) => {
              if (e.target.files[0]) {
                setAvatar(URL.createObjectURL(e.target.files[0]));
              }
            }}
          />
        </div>
        <div className="form-group">
          <label htmlFor="signup-username">Username</label>
          <input
            type="text"
            id="signup-username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="Choose a username"
            required
          />
        </div>
        <div className="form-group">
          <label htmlFor="signup-email">Email Address</label>
          <input
            type="email"
            id="signup-email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email"
            required
          />
        </div>
        <div className="form-group">
          <label htmlFor="signup-password">Password</label>
          <div className="password-container">
            <input
              type={showPassword ? "text" : "password"}
              id="signup-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Create a password"
              required
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? '🙈' : '👁️'}
            </button>
          </div>
        </div>
        <div className="form-group">
          <label htmlFor="confirm-password">Confirm Password</label>
          <div className="password-container">
            <input
              type={showConfirmPassword ? "text" : "password"}
              id="confirm-password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Confirm your password"
              required
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? '🙈' : '👁️'}
            </button>
          </div>
        </div>
        {error && <div className="error-message">{error}</div>}
        <button type="submit" className="submit-btn" disabled={isLoading}>
          {isLoading ? 'Creating Account...' : 'Join the Adventure'}
        </button>
      </form>
    </div>
  );
};

export default SignUpPage;
