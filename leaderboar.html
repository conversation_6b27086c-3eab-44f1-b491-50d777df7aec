<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Riddle Realm of Dread - Leaderboard</title>
    
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />
    <link rel="stylesheet" href="index2.css">
    <link rel="stylesheet" href="leaderboard.css">
</head>
<body>
    <div class="container">
        <div class="boxcontainer leaderboard-container">
            <h1>Leaderboard</h1>
            
            <div class="leaderboard-filters">
                <div class="filter-group">
                    <label for="timeframe">Timeframe:</label>
                    <select id="timeframe" class="form-select">
                        <option value="all-time">All Time</option>
                        <option value="monthly">Monthly</option>
                        <option value="weekly">Weekly</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="category">Category:</label>
                    <select id="category" class="form-select">
                        <option value="all">All Categories</option>
                        <option value="mathematics">Mathematics</option>
                        <option value="general">General Riddles</option>
                        <option value="word">Word Riddles</option>
                        <option value="logic">Logic Riddles</option>
                    </select>
                </div>
            </div>

            <!-- User Position Search Section -->
            <div class="user-position-search">
                <input type="text" id="user-search" class="form-control" placeholder="Search for a nickname">
                <button id="find-user" class="btn btn-md">Find User</button>
                <button id="show-my-position" class="btn btn-md">Show My Position</button>
            </div>

            <!-- User Position Display -->
            <div id="user-position-result" class="user-position-result"></div>

            <table class="leaderboard-table">
                <thead>
                    <tr>
                        <th>Rank</th>
                        <th>Nickname</th>
                        <th>Points</th>
                        <th>Total Riddles Solved</th>
                        <th>Accuracy</th>
                    </tr>
                </thead>
                <tbody id="leaderboard-body">
                    <!-- Leaderboard entries will be dynamically populated here -->
                </tbody>
            </table>

            <div class="leaderboard-pagination">
                <button id="prev-page" class="btn btn-md">Previous</button>
                <span id="current-page">Page 1</span>
                <button id="next-page" class="btn btn-md">Next</button>
            </div>

            <div class="back">
                <button type="button" class="btn btn-md" id="back-to-main">Back to Main Menu</button>
            </div>
        </div>
    </div>

    <script src="leaderboard.js"></script>
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.min.js"
      integrity="sha384-BBtl+eGJRgqQAUMxJ7pMwbEyER4l1g+O15P+16Ep7Q9Q+zqX6gSbd85u4mG4QzX+"
      crossorigin="anonymous"
    ></script>
</body>
</html>