<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Riddle Realm of Dread</title>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/GameMode.css">
</head>

<body>
    <div class="boxcontainer">
        <h1 id="riddle-realm-of-dread">Riddle Realm of Dread</h1>
        <h2>{{.Riddletype}}</h2>
        <h4> Select mode</h4>
        <div>
          <a href="{{.Classic}}" style="text-decoration: none;"> <button class="btn" id="classic"><i class="ri-book-open-line"></i>Classic Mode</button></a>
           <a href="{{.Timeattack}}" style="text-decoration: none;"> <button class="btn" id="time"><i class="ri-timer-line"></i>Time Attack</button></a>
            <a href="{{.Survival}}"><button class="btn" id="survival"><i class="ri-skull-line"></i>Survival Mode</button></a>
            <button class="btn difficulty-btn" id="difficulty"><i class="ri-settings-3-line"></i>Select Difficulty</button>
        </div>

        <div class="back">
            <a href="/selectriddlecategory" class="back-btn">Back</a>
        </div>
    </div>
    <script src="../js/GameMode.js"></script>
</body>
</html>