<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Time Attack - Survival Mode</title>
</head>
<link rel="stylesheet" href="../css/survival.css">
<script defer src="../js/survival.js"></script>
<body>
    <div class="survival-mode-container">
        <div class="game-header">
            <div class="timer-container">
                <span class="timer-label">Time Left:</span>
                <span class="timer">30</span>
                <span class="timer-unit">s</span>
            </div>
            <div class="lives-container">
                Lives: <span class="lives-count">3</span>
            </div>
        </div>

        <div class="challenge-section">
            <h1 class="challenge-title">Survival Mode</h1>
            <div class="challenge-description">
                Answer correctly to survive! Lose all your lives and the game ends.
            </div>
        </div>


        <div id="hint" class="hint" style="display:none;">
            
        </div>

        <div class="riddle">
            <h2>What travels around the world but stays in one corner?</h2>
        </div>

        <div class="answer-buttons">
            <button class="btn">A Stamp</button>
            <button class="btn">A Letter</button>
            <button class="btn">A Postcard</button>
            <button class="btn">An Envelope</button>
        </div>


        <div id="custom-answer-container" class="displaycustom">
            <input type="text" id="custom-answer" class="custom-answer-input" placeholder="Enter your answer">
            <button class="btn custom-answer-submit">Submit</button>
        </div>

        <div class="game-footer">
            <div class="progress-tracker">
                Correct Answers: <span class="correct-count">0</span> / <span class="total-challenges">10</span>
            </div>

            <div class="game-actions" >
                <a href="#" class="hint-link">Need a Hint?</a><br>
                <a href="#" class="custom-answer-link">Custom Answer</a>
            </div>

            <div id="feedback" class="feedback"></div>
        </div>
    </div>

  
</body>
</html>