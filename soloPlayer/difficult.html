<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Difficulty Selection - Riddle Realm of Dread</title>
    <link href="../css/difficult.css" rel="stylesheet">
</head>
<body>
    <div class="boxcontainer">
        <input type="hidden" value="{{.Difficult}}" id="playprefferedDifficulty">
        <h1>Difficulty Selection</h1>
        <div class="difficulty-selection">
            <button class="btn difficulty-btn active" data-difficulty="easy">
                <i class="fa fa-skull-crossbones"></i>Novice Curse(easy)
            </button>
            <button class="btn difficulty-btn" data-difficulty="medium">
                <i class="fa fa-ghost"></i>Haunting Challenge(medium)
            </button>
            <button class="btn difficulty-btn" data-difficulty="hard">
                <i class="fa fa-demon"></i>Realm of Despair(hard)
            </button>
        </div>
        <div class="difficulty-description">
            <p id="difficulty-info">Choose the darkness that matches your courage...</p>
        </div>
        <div class="back">
            <a href="#" class="back-btn" id="back">Return to Modes</a>
        </div>
    </div>

   <script src="../js/difficult.js"></script>
</body>
</html>