<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Riddle Realm of Dread - Game</title>
    <!-- <link rel="stylesheet" href="starterbootstrap.css"> -->
    <link rel="stylesheet" href="/css/playsection.css">
</head>
<body>
    <div class="boxcontainer game-container">
        <div class="game-header">
            <div class="timer">Time: 60s</div>
               
            <div class="level-indicator">
                Level: <span class="current-level">2</span>
            </div>
            <div class="riddle-info">
                Riddle :<span class="current-riddle">1</span>/<span class="total-riddles">5</span>
            </div>
            
            <div class="score">Score: 100</div>
        </div>

        <div class="riddle">
            <h2 id="riddleQustion"></h2>
        </div>

        <div id="hint" class="hint" style="display: none;">
            Hint: <span id="riddleHint">rrrrrrrrrrr</span>
        </div>

        <div class="answer-buttons">
            <button class="btn choices" id="choice1"></button>
            <button class="btn choices" id="choice2"></button>
            <button class="btn choices" id="choice3"></button>
            <button class="btn choices" id="choice4"></button>
        </div>

        <div id="custom-answer-container" class="custom-answer-container" style="display: none;">
            <input type="text" id="custom-answer" class="custom-answer-input" placeholder="Enter your answer">
            <button class="btn custom-answer-submit">Submit</button>
        </div>

        <div class="game-footer">
            <span class="hint-link">Need a Hint?</span>
            <!-- <span class="custom-answer-link" >Custom Answer</span> -->
            <div id="feedback" class="feedback"></div>
        </div>
    </div>

    <script src="/js/playsection.js"></script>
</body>
</html>