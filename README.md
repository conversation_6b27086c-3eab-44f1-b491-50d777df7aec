# Riddle Truth or Dare Project

This project is a Riddle Truth or Dare game, built with HTML, CSS, and JavaScript. It combines two popular elements: riddles and traditional truth-or-dare challenges. The player is presented with riddles, and if they fail to solve them, they must complete a dare.

## Overview

The game consists of two main components:
1. **Riddles**: Players are presented with a random riddle to solve.
2. **Dares**: If a player fails to answer the riddle correctly, they are assigned a dare as a consequence.

The player can interact with the game by attempting to answer riddles and, when necessary, performing dares.

## How It Works

### Riddles
- The game randomly selects a riddle from a predefined pool of riddles.
- Players are given a limited amount of time to solve each riddle.
- If the player answers correctly, they proceed to the next riddle.
- If the player fails to answer correctly within the given time or submits an incorrect answer, they must complete a dare.

### Dares
- Dares are pre-defined in the game and are assigned when a player fails to answer a riddle.
- Players can choose the difficulty level of dares at the start of the game (easy, medium, or hard).
- The game ensures that dares are varied and appropriate for the selected difficulty level.

### Customization
- Players can select the theme of the dares (e.g., funny, embarrassing, or challenging) before starting the game.
- The game allows for group play, where players can take turns answering riddles and performing dares.
- Optional features may include tracking points or scoring for correct answers.

## Key Components

1. **Riddle Pool**: 
   - A collection of riddles stored in the game, with varying levels of difficulty.
   - The game randomly selects a riddle from this pool for each round.

2. **Dare Assignment**:
   - Pre-defined dares are categorized into difficulty levels.
   - When a player fails to answer a riddle, they are assigned a dare based on the selected difficulty and theme.

3. **Customizable Settings**:
   - Players can choose dare difficulty and themes before starting the game.
   - The game can be played individually or in a group, with different modes for party settings or casual play.


