import express from 'express';
import cors from 'cors';
import bcrypt from 'bcryptjs';
import db from './database/dbquery.js';

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
    origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:3000'],
    credentials: true
}));
app.use(express.json());

// Database queries
const insertUser = db.prepare(`
    INSERT INTO users (email, username, password_hash, profile_pic, nickname)
    VALUES (?, ?, ?, ?, ?)
`);

const getUserByEmail = db.prepare(`
    SELECT * FROM users WHERE email = ?
`);

const getUserByUsername = db.prepare(`
    SELECT * FROM users WHERE username = ?
`);

// Helper functions
const hashPassword = async (password) => {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
};

const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

const validatePassword = (password) => {
    // Check if password meets strength requirements
    if (password.length < 6) return false;
    
    let strength = 0;
    if (password.length >= 6) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
    
    return strength >= 4; // Require strong password
};

// Routes
app.post('/api/register', async (req, res) => {
    try {
        const { username, email, password, avatar } = req.body;

        // Validation
        if (!username || !email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Username, email, and password are required'
            });
        }

        if (username.length < 3) {
            return res.status(400).json({
                success: false,
                message: 'Username must be at least 3 characters long'
            });
        }

        if (!validateEmail(email)) {
            return res.status(400).json({
                success: false,
                message: 'Please enter a valid email address'
            });
        }

        if (!validatePassword(password)) {
            return res.status(400).json({
                success: false,
                message: 'Password must be at least 6 characters long and contain uppercase, lowercase, number, and special character'
            });
        }

        // Check if user already exists
        const existingUserByEmail = getUserByEmail.get(email);
        if (existingUserByEmail) {
            return res.status(409).json({
                success: false,
                message: 'An account with this email already exists'
            });
        }

        const existingUserByUsername = getUserByUsername.get(username);
        if (existingUserByUsername) {
            return res.status(409).json({
                success: false,
                message: 'This username is already taken'
            });
        }

        // Hash password
        const hashedPassword = await hashPassword(password);

        // Insert user into database
        const result = insertUser.run(
            email,
            username,
            hashedPassword,
            avatar || 'default image',
            username // Use username as nickname by default
        );

        // Return success response
        res.status(201).json({
            success: true,
            message: 'Account created successfully!',
            user: {
                id: result.lastInsertRowid,
                username,
                email,
                avatar: avatar || 'default image'
            }
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error. Please try again later.'
        });
    }
});

// Login endpoint (for future use)
app.post('/api/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Email and password are required'
            });
        }

        const user = getUserByEmail.get(email);
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Invalid email or password'
            });
        }

        const isValidPassword = await bcrypt.compare(password, user.password_hash);
        if (!isValidPassword) {
            return res.status(401).json({
                success: false,
                message: 'Invalid email or password'
            });
        }

        res.json({
            success: true,
            message: 'Login successful!',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                avatar: user.profile_pic,
                level: user.level
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error. Please try again later.'
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', message: 'Server is running' });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Backend server is running on http://localhost:${PORT}`);
    console.log(`📊 Database connected successfully`);
});
