// Import modules
import express from 'express';
import { config } from 'dotenv';

// Load environment variables
config();

// Create express app
const app = express();

//Use middleware to parse JSON (if needed)
//This line sets up middleware in your Express app to automatically parse incoming JSON payloads in HTTP requests.
app.use(express.json())

app.post("/auth",(req,res)=>{
    console.log(req.body)
    res.send('<h1>Hello from Express!</h1>');
})

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
})