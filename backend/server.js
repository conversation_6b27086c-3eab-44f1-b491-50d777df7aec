// Import modules
import express from 'express';
import cors from 'cors';
import bcrypt from 'bcryptjs';
import Database from 'better-sqlite3';
import { config } from 'dotenv';

// Load environment variables
config();

// Create express app
const app = express();

// Create database connection
const db = new Database('riddles.db');

// Create users table if it doesn't exist
// This table stores user information, including their email, username, hashed password, profile picture, level, and nickname.
// The schema is based on the ER diagram provided in the project documentation.
db.prepare(`
  CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT UNIQUE NOT NULL,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    profile_pic TEXT,
    level INTEGER DEFAULT 1,
    nickname TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )
`).run();

// Middleware
app.use(cors({
    origin: ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:5174', 'http://********:3000'],
    credentials: true
}));
app.use(express.json());

// Database queries
// Prepared statements are used to prevent SQL injection attacks.
const insertUser = db.prepare(`
    INSERT INTO users (email, username, password_hash, profile_pic, nickname)
    VALUES (?, ?, ?, ?, ?)
`);

const getUserByEmail = db.prepare(`
    SELECT * FROM users WHERE email = ?
`);

const getUserByUsername = db.prepare(`
    SELECT * FROM users WHERE username = ?
`);

// Helper functions
// This function hashes the user's password using bcrypt.
// A salt round of 12 is used for a strong hash.
const hashPassword = async (password) => {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
};

// This function validates the user's email address using a regular expression.
const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

// This function validates the user's password to ensure it meets the strength requirements.
// The password must be at least 6 characters long and contain at least 2 of the following: uppercase, lowercase, number, special character.
const validatePassword = (password) => {
    // Check if password meets strength requirements
    if (password.length < 6) return false;

    let strength = 0;
    if (password.length >= 6) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;

    return strength >= 2; // Require at least medium strength
};

// Routes
// This route handles user registration.
// It validates the user's input, checks if the user already exists, hashes the password, and inserts the user into the database.
app.post('/auth', async (req, res) => {
    try {
        const { username, email, password, avatar } = req.body;

        console.log('Registration attempt:', { username, email, avatar });

        // Validation
        if (!username || !email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Username, email, and password are required'
            });
        }

        if (username.length < 3) {
            return res.status(400).json({
                success: false,
                message: 'Username must be at least 3 characters long'
            });
        }

        if (!validateEmail(email)) {
            return res.status(400).json({
                success: false,
                message: 'Please enter a valid email address'
            });
        }

        if (!validatePassword(password)) {
            return res.status(400).json({
                success: false,
                message: 'Password must be at least 6 characters long and contain at least 2 of: uppercase, lowercase, number, special character'
            });
        }

        // Check if user already exists
        const existingUserByEmail = getUserByEmail.get(email);
        if (existingUserByEmail) {
            return res.status(409).json({
                success: false,
                message: 'An account with this email already exists'
            });
        }

        const existingUserByUsername = getUserByUsername.get(username);
        if (existingUserByUsername) {
            return res.status(409).json({
                success: false,
                message: 'This username is already taken'
            });
        }

        // Hash password
        const hashedPassword = await hashPassword(password);

        // Insert user into database
        const result = insertUser.run(
            email,
            username,
            hashedPassword,
            avatar || 'default image',
            username // Use username as nickname by default
        );

        console.log('User created successfully:', { id: result.lastInsertRowid, username, email });

        // Return success response
        res.status(201).json({
            success: true,
            message: 'Account created successfully!',
            user: {
                id: result.lastInsertRowid,
                username,
                email,
                avatar: avatar || 'default image'
            }
        });

    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error. Please try again later.'
        });
    }
});

// This route handles user login.
// It validates the user's input, checks if the user exists, and compares the password with the hashed password in the database.
app.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        if (!email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Email and password are required'
            });
        }

        const user = getUserByEmail.get(email);
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Invalid email or password'
            });
        }

        const isValidPassword = await bcrypt.compare(password, user.password_hash);
        if (!isValidPassword) {
            return res.status(401).json({
                success: false,
                message: 'Invalid email or password'
            });
        }

        res.json({
            success: true,
            message: 'Login successful!',
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                avatar: user.profile_pic,
                level: user.level
            }
        });

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error. Please try again later.'
        });
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'OK', message: 'Server is running' });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`🚀 Backend server is running on http://localhost:${PORT}`);
  console.log(`📊 Database connected successfully`);
});
