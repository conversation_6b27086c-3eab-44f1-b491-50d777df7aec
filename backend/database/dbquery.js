import Database from "better-sqlite3";

// This creates the DB file if it doesn't exist
const db = new Database("riddles.db");

//users table
db.prepare(`
  CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT UNIQUE NOT NULL,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    profile_pic TEXT,
    level INTEGER DEFAULT 1,
    nickname TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )
`).run();

//riddle table
db.prepare(`
  CREATE TABLE IF NOT EXISTS riddles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    explanation TEXT,
    category TEXT,
    theme TEXT,
    difficulty TEXT,
    points INTEGER DEFAULT 0
  )
`).run();

//truth or dare table
db.prepare(`
  CREATE TABLE IF NOT EXISTS truth_or_dare (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    prompt TEXT NOT NULL,
    type TEXT CHECK(type IN ('truth', 'dare')) NOT NULL,
    difficulty TEXT
  )
`).run();


//Purpose: Store game sessions (for multiplayer or solo sessions)
db.prepare(`CREATE TABLE IF NOT EXISTS games (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    mode TEXT CHECK(mode IN ('solo', 'multiplayer')) NOT NULL,
    type TEXT CHECK(type IN ('riddle', 'truth', 'dare', 'mixed')) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id)
);`)


//Purpose: Associate users with a multiplayer game session.
db.prepare(`
  CREATE TABLE IF NOT EXISTS game_players (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    game_id INTEGER NOT NULL,
    player_name TEXT NOT NULL,
    FOREIGN KEY(game_id) REFERENCES games(id)
  )
`).run();

//Purpose: Track user achievements and solved riddles.
db.prepare(`CREATE TABLE IF NOT EXISTS user_progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    riddle_id INTEGER NOT NULL,
    solved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    points_earned INTEGER,
    difficulty TEXT,
    FOREIGN KEY(user_id) REFERENCES users(id),
    FOREIGN KEY(riddle_id) REFERENCES riddles(id)
);`)

//Purpose: Cache leaderboard rankings.
db.prepare(`CREATE TABLE IF NOT EXISTS leaderboard (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    total_points INTEGER DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id)
);`)

//settings table
db.prepare(`
  CREATE TABLE IF NOT EXISTS settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    sound BOOLEAN DEFAULT 1,
    notifications BOOLEAN DEFAULT 1,
    FOREIGN KEY(user_id) REFERENCES users(id)
  )
`).run();



export default db;
