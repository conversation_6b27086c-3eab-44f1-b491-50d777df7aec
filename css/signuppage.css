@import url('https://fonts.googleapis.com/css2?family=Nosifer&family=Merriweather:wght@400;700&display=swap');

:root {
    --dark-bg: #121212;
    --blood-red: #8B0000;
    --muted-red: #5D1E1E;
    --text-glow: rgba(139, 0, 0, 0.7);
}

body {
display: flex;
justify-content: center;
align-items: center;
min-height: 100vh;
padding: 0;
margin: 0;
background-image: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url("../images/dark\ forest\ picture.jpg");
background-repeat: no-repeat;
background-size: cover;
background-attachment: fixed;
font-family: 'Merriweather', serif;
color: #ffffff;
}

.container {
width: 100%;
max-width: 100%;
padding: 0;
}

.row {
display: flex;
justify-content: center;
align-items: center;
min-height: 100vh;
margin: 0;
}

.col-md-8 {
max-width: 500px;
width: 100%;
padding: 0 15px;
}

body::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(139,0,0,0.1), rgba(0,0,0,0.5));
    animation: bg-shift 15s ease infinite;
    pointer-events: none;
    z-index: -1;
}

.signup-container {
    width: 100%;
    max-width: 500px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 20px;
    box-shadow: 0 0 30px rgba(139, 0, 0, 0.5);
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 2px solid var(--blood-red);
}

.error-message {
    background-color: rgba(255, 0, 0, 0.2);
    color: #ff4500;
    border: 2px solid #ff4500;
    border-radius: 10px;
    padding: 10px;
    margin-bottom: 20px;
    width: 100%;
    text-align: center;
    display: none;
}
/* Update these CSS rules in signuppage.css */
.avatar-selection {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
    padding: 10px;
}

.avatar-option {
    width: 120px;
    height: 150px;
    border: 3px solid transparent;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    background: rgba(0, 0, 0, 0.4);
}

.avatar-option img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 8px;
}

.avatar-label {
    font-family: 'Merriweather', serif;
    color: #ffffff;
    font-size: 0.9rem;
    text-align: center;
    margin-top: auto;
}

.avatar-option:hover {
    transform: translateY(-5px);
    border-color: var(--blood-red);
    background: rgba(93, 30, 30, 0.3);
}

.avatar-option.selected {
    border-color: #ff4500;
    box-shadow: 0 0 20px var(--text-glow);
    background: rgba(139, 0, 0, 0.3);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .avatar-option {
        width: 100px;
        height: 130px;
    }

    .avatar-option img {
        width: 60px;
        height: 60px;
    }

    .avatar-label {
        font-size: 0.8rem;
    }
}

h1 {
    font-family: 'Nosifer', cursive;
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: transparent; 
    -webkit-text-stroke: 2px var(--blood-red);
    text-shadow: 0 0 15px var(--text-glow);
    background-image: linear-gradient(45deg, var(--blood-red), #ff4500);
    -webkit-background-clip: text;
    background-clip: text;
    animation: blood-pulse 1s ease-in-out infinite alternate;
}

.signup-form {
    width: 100%;
    max-width: 500px;
}

.form-control {
    margin-bottom: 15px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: 2px solid var(--blood-red);
    transition: all 0.3s ease;
}

.form-control::placeholder {
    color: rgba(255,255,255,0.6);
    font-family: 'Merriweather', serif;
}

.form-control:focus {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-color: #ff4500;
    box-shadow: 0 0 15px var(--text-glow);
    outline: none;
}

.avatar-selection {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.avatar-option {
    width: 100px;
    height: 100px;
    border: 3px solid transparent;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    background-size: cover;
    background-position: center;
    box-shadow: 0 0 10px rgba(0,0,0,0.5);
}

.avatar-option:hover {
    transform: scale(1.1);
    border-color: var(--blood-red);
}

.avatar-option.selected {
    border-color: #ff4500;
    box-shadow: 0 0 20px var(--text-glow);
    transform: scale(1.1);
}

.btn-signup {
    padding: 15px 25px;
    margin: 15px;
    width: 250px;
    border: none;
    border-radius: 10px;
    background-image: linear-gradient(to right, var(--blood-red), #5D1E1E);
    color: white;
    font-family: 'Nosifer', sans-serif;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(139, 0, 0, 0.5);
}

.btn-signup:hover {
    transform: scale(1.05);
    box-shadow: 0 0 25px rgba(139, 0, 0, 0.7);
}

.login-link {
    margin-top: 15px;
    color: white;
    text-decoration: none;
    font-family: 'Merriweather', serif;
    transition: color 0.3s ease;
}

.login-link:hover {
    color: var(--blood-red);
    text-decoration: underline;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    body {
        margin: 5%;
        margin-top: 2%;
    }

    h1 {
        font-size: 2rem;
    }

    .signup-container {
        padding: 20px;
    }

    .avatar-option {
        width: 80px;
        height: 80px;
    }

    .btn-signup {
        width: 100%;
        margin: 15px 0;
    }
}

@keyframes blood-pulse {
    0% {
        text-shadow: 0 0 10px var(--text-glow);
        transform: scale(1);
    }
    100% {
        text-shadow: 0 0 25px var(--text-glow);
        transform: scale(1.03);
    }
}

@keyframes bg-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}
/* Add these styles to your existing CSS file */

.social-signup {
    width: 100%;
    text-align: center;
    margin-bottom: 20px;
}

.social-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin: 15px 0;
}

.btn-social {
    width: 120px;
    padding: 10px;
    border: 2px solid var(--blood-red);
    background: rgba(0, 0, 0, 0.5);
    color: white;
    font-family: 'Merriweather', serif;
    font-size: 14px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-google:hover {
    background: rgba(219, 68, 55, 0.2);
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(219, 68, 55, 0.3);
}

.btn-facebook:hover {
    background: rgba(66, 103, 178, 0.2);
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(66, 103, 178, 0.3);
}

.divider {
    width: 100%;
    text-align: center;
    border-bottom: 1px solid rgba(139, 0, 0, 0.3);
    line-height: 0.1em;
    margin: 20px 0;
}

.divider span {
    background: rgba(0, 0, 0, 0.6);
    padding: 0 10px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

/* Update responsive styles */
@media (max-width: 576px) {
    .social-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-social {
        width: 200px;
    }
}

/* Ensure the avatar selection grid stays clean with the new social buttons */
.avatar-selection {
    margin-top: 20px;
}