@import url('https://fonts.googleapis.com/css2?family=Nosifer&family=Merriweather:wght@400;700&display=swap');

:root {
    --dark-bg: #121212;
    --blood-red: #8B0000;
    --muted-red: #5D1E1E;
    --text-glow: rgba(139, 0, 0, 0.7);
}

body {
    padding: 0;
    margin: 15%;
    margin-top: 4%;
    background-image: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url("../images/dark\ forest\ picture.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    height: 100vh;
    font-family: 'Merriweather', serif;
    color: #ffffff;
}

body::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(139,0,0,0.1), rgba(0,0,0,0.5));
    animation: bg-shift 15s ease infinite;
    pointer-events: none;
    z-index: -1;
}

.boxcontainer {
    width: 75vw;
    height: auto;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 20px;
    box-shadow: 0 0 30px rgba(139, 0, 0, 0.5);
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 2px solid var(--blood-red);
}

h1 {
    font-family: 'Nosifer', cursive;
    font-size: 50px;
    margin-bottom: 20px;
    color: transparent; 
    -webkit-text-stroke: 2px var(--blood-red);
    text-shadow: 0 0 15px var(--text-glow);
    background-image: linear-gradient(45deg, var(--blood-red), #ff4500);
    -webkit-background-clip: text;
    background-clip: text;
    animation: blood-pulse 1s ease-in-out infinite alternate;
}

.game-modes {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.game-mode {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid var(--blood-red);
    border-radius: 15px;
    padding: 20px;
    transition: all 0.3s ease;
}

.game-mode:hover {
    transform: scale(1.03);
    box-shadow: 0 0 20px rgba(139, 0, 0, 0.5);
}

.game-mode h2 {
    font-family: 'Nosifer', cursive;
    color: var(--blood-red);
    margin-bottom: 15px;
}

.game-mode p {
    color: #ffffff;
    line-height: 1.6;
}

.mode-variants {
    margin-top: 15px;
    background: rgba(139, 0, 0, 0.1);
    border-radius: 10px;
    padding: 15px;
}

.mode-variants h3 {
    color: var(--blood-red);
    margin-bottom: 10px;
}

.mode-variants ul {
    list-style-type: none;
    padding: 0;
    text-align: left;
}

.mode-variants ul li {
    padding: 5px 0;
    border-bottom: 1px solid rgba(139, 0, 0, 0.2);
}

.mode-variants ul li:last-child {
    border-bottom: none;
}

.warning {
    background: rgba(139, 0, 0, 0.2);
    border: 2px solid var(--blood-red);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 20px;
}

.warning h3 {
    color: var(--blood-red);
    margin-bottom: 10px;
}

@keyframes blood-pulse {
    0% {
        text-shadow: 0 0 10px var(--text-glow);
        transform: scale(1);
    }
    100% {
        text-shadow: 0 0 25px var(--text-glow);
        transform: scale(1.03);
    }
}

@keyframes bg-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.btn {
    padding: 15px 25px;
    border: none;
    border-radius: 10px;
    background-image: linear-gradient(to right, var(--blood-red), #5D1E1E);
    color: white;
    font-family: 'Nosifer', sans-serif;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 15px rgba(139, 0, 0, 0.5);
}

.btn:hover {
    transform: scale(1.05);
    box-shadow: 0 0 25px rgba(139, 0, 0, 0.7);
}

@media screen and (max-width: 768px) {
    .game-modes {
        grid-template-columns: 1fr;
    }

    h1 {
        font-size: 35px;
    }
}