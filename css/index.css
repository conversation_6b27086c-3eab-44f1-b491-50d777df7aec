@import url('https://fonts.googleapis.com/css2?family=Nosifer&family=Merriweather:wght@400;700&display=swap');

:root {
    --dark-bg: #121212;
    --blood-red: #8B0000;
    --muted-red: #5D1E1E;
    --text-glow: rgba(139, 0, 0, 0.7);
}

body{
    padding: 0;
    margin:15%;
    margin-top: 4%;
    /* display: flex;
    justify-content: center;
    align-items: center; */
    background-image: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url("../images/dark\ forest\ picture.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    height: 100vh;
    font-family: 'Merriweather', serif;
    color: #ffffff;
}
body::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(139,0,0,0.1), rgba(0,0,0,0.5));
    animation: bg-shift 15s ease infinite;
    pointer-events: none;
    z-index: -1;
}


.boxcontainer{
    width: 75vw;
    height: auto;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 20px;
    box-shadow: 0 0 30px rgba(139, 0, 0, 0.5);
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 2px solid var(--blood-red);
}

h1{
    font-family: 'Nosifer', cursive;
    font-size: 50px;
    margin-bottom: 20px;
    color: transparent; 
    -webkit-text-stroke: 2px var(--blood-red);
    text-shadow: 0 0 15px var(--text-glow);
    background-image: linear-gradient(45deg, var(--blood-red), #ff4500);
    -webkit-background-clip: text;
    background-clip: text;
    animation: blood-pulse 1s ease-in-out infinite alternate;
}

@keyframes blood-pulse {
    0% {
        text-shadow: 0 0 10px var(--text-glow);
        transform: scale(1);
    }
    100% {
        text-shadow: 0 0 25px var(--text-glow);
        transform: scale(1.03);
    }
}

.btn {
    padding: 15px 25px;
    margin: 15px;
    width: 250px;
    border: none;
    border-radius: 10px;
    background-image: linear-gradient(to right, var(--blood-red), #5D1E1E);
    color: white;
    font-family: 'Nosifer', sans-serif;
    font-size: 15px;
    align-items: center;
    justify-content: center;
    display: flex;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(139, 0, 0, 0.5);
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(120deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: all 0.6s;
}

.btn:hover:before {
    left: 100%;
}

.btn:hover {
    transform: scale(1.05);
    box-shadow: 0 0 25px rgba(139, 0, 0, 0.7);
}

/* Responsive Adjustments */
@media screen and (max-width: 768px) {
    #containters {
        width: 90vw;
        height: 85vh;
    }

    h1#riddle-realm-of-dread {
        font-size: 35px;
    }

    .btn {
        width: 200px;
        font-size: 16px;
    }
}

/* Optional: Add some eerie icons */
.btn-icon {
    margin-right: 10px;
    vertical-align: middle;
}

/* Subtle background animation */
@keyframes bg-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}



.riddle-categories {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
    padding: 20px;
    border: 5px solid white;
}

.riddle-category {
    width: 180px;
    height: 180px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid #8B0000;
    box-shadow: 0 0 20px rgba(139, 0, 0, 0.3);
    cursor: pointer;
}

.riddle-category:hover {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(139, 0, 0, 0.5);
}

.riddle-category svg {
    width: 100px;
    height: 100px;
    margin-bottom: 15px;
    stroke: #8B0000;
    fill: none;
}

.riddle-category h3 {
    color: white;
    font-family: 'Nosifer', cursive;
    margin-top: 15px;
    font-size: 15px;
}
.back{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media screen and (max-width: 768px) {
    .riddle-categories {
        flex-direction: row;
    }
    .riddle-category {
        width: 200px;
        height: 200px;
    }
}