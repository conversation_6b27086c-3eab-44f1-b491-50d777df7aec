@import url('https://fonts.googleapis.com/css2?family=Nosifer&family=Merriweather:wght@400;700&display=swap');

:root {
    --dark-bg: #121212;
    --blood-red: #8B0000;
    --muted-red: #5D1E1E;
    --text-glow: rgba(139, 0, 0, 0.7);
    --time-attack-accent: #FF4500;
}

body {
    padding: 0;
    margin: 15%;
    margin-top: 4%;
    background-image: linear-gradient(rgba(0,0,0,0.8), rgba(0,0,0,0.8)), url("../images/dark-forest-time-attack.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    height: 100vh;
    font-family: 'Merriweather', serif;
    color: #ffffff;
}

.time-attack-container {
    width: 75vw;
    height: auto;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 20px;
    box-shadow: 0 0 40px rgba(255, 69, 0, 0.5);
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    backdrop-filter: blur(15px);
    border: 3px solid var(--time-attack-accent);
}

.game-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 20px;
    color: white;
    font-family: 'Merriweather', serif;
}

.timer-container {
    background: rgba(255, 69, 0, 0.3);
    padding: 10px 15px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: bold;
}

.timer {
    color: var(--time-attack-accent);
    font-size: 1.5rem;
    font-family: 'Nosifer', sans-serif;
}

.score-container {
    background: rgba(255, 69, 0, 0.3);
    padding: 10px 15px;
    border-radius: 15px;
    font-weight: bold;
}

.challenge-section {
    margin-bottom: 30px;
    text-align: center;
}

.challenge-title {
    font-family: 'Nosifer', sans-serif;
    color: var(--time-attack-accent);
    font-size: 2rem;
    margin-bottom: 10px;
    text-shadow: 0 0 15px rgba(255, 69, 0, 0.5);
}

.challenge-description {
    color: white;
    font-style: italic;
    margin-bottom: 20px;
}

.riddle {
    margin-bottom: 30px;
    font-family: 'Merriweather', serif;
    color: white;
}

.riddle h2 {
    font-size: 1.5rem;
    line-height: 1.6;
}

.answer-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    width: 100%;
}

.btn {
    padding: 15px 25px;
    width: 250px;
    border: none;
    border-radius: 10px;
    background-image: linear-gradient(to right, var(--time-attack-accent), var(--muted-red));
    color: white;
    font-family: 'Nosifer', sans-serif;
    font-size: 15px;
    align-items: center;
    justify-content: center;
    display: flex;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(255, 69, 0, 0.5);
}

.btn:hover {
    transform: scale(1.05);
    box-shadow: 0 0 30px rgba(255, 69, 0, 0.7);
}

.game-footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    gap: 15px;
}

.progress-tracker {
    background: rgba(255, 69, 0, 0.3);
    padding: 10px 15px;
    border-radius: 15px;
    font-weight: bold;
}

.game-actions {
    display: flex;
    flex-direction:column;
    gap: 20px;
}

.hint {
    background: rgba(139, 0, 0, 0.3);
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    font-family: 'Merriweather', serif;
}

.hint-link, .custom-answer-link {
    color: var(--time-attack-accent);
    font-family: 'Merriweather', serif;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    font-weight: bold;
}

.hint-link:hover, .custom-answer-link:hover {
    color: #ff6347;
    text-shadow: 0 0 10px rgba(255, 69, 0, 0.5);
    transform: scale(1.05);
}

.feedback {
    margin-top: 10px;
    color: white;
    font-family: 'Merriweather', serif;
}

@media screen and (max-width: 768px) {
    .time-attack-container {
        width: 90vw;
        padding: 20px;
    }

    .riddle h2 {
        font-size: 1.2rem;
    }

    .challenge-title {
        font-size: 1.5rem;
    }
}