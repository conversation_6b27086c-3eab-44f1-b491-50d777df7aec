@import url('https://fonts.googleapis.com/css2?family=Nosifer&family=Merriweather:wght@400;700&display=swap');

:root {
    --dark-bg: #121212;
    --blood-red: #8B0000;
    --muted-red: #5D1E1E;
    --text-glow: rgba(139, 0, 0, 0.7);
    --input-bg: rgba(0, 0, 0, 0.5);
}

body {
    background-image: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url("../images/dark forest picture.jpg");
    background-size: cover;
    background-attachment: fixed;
    color: white;
    font-family: 'Merriweather', serif;
    line-height: 1.6;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
.container {
    max-width: 1200px;
    margin: 0;
    padding: 1rem;
}

@media (min-width: 768px) {
    .container {
        margin: 2rem auto;
        padding: 2rem;
    }
}

/* Profile Header */
.profile-header {
    text-align: center;
    margin-bottom: 2rem;
}

.profile-header h1 {
    font-family: 'Nosifer', cursive;
    color: var(--blood-red);
    text-shadow: 0 0 15px var(--text-glow);
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

@media (min-width: 768px) {
    .profile-header h1 {
        font-size: 2.5rem;
    }
}

/* Profile Grid */
.profile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    background: rgba(0, 0, 0, 0.7);
    padding: 1rem;
    border-radius: 15px;
    border: 2px solid var(--blood-red);
    box-shadow: 0 0 30px var(--text-glow);
}

@media (min-width: 992px) {
    .profile-grid {
        grid-template-columns: 1fr 2fr;
        padding: 2rem;
        gap: 2rem;
    }
}

/* Avatar Section */
.avatar-section {
    text-align: center;
    padding: 1rem;
}

.current-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 3px solid var(--blood-red);
    margin: 0 auto 1rem;
    overflow: hidden;
    box-shadow: 0 0 20px var(--text-glow);
}

@media (min-width: 768px) {
    .current-avatar {
        width: 200px;
        height: 200px;
    }
}

.current-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-gallery {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin-top: 1rem;
    justify-items: center;
}

@media (min-width: 768px) {
    .avatar-gallery {
        gap: 1rem;
    }
}

.avatar-option {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
}

.avatar-option:hover {
    transform: scale(1.1);
    border-color: var(--blood-red);
}

.avatar-option.selected {
    border-color: var(--blood-red);
    box-shadow: 0 0 15px var(--text-glow);
}

@media (min-width: 768px) {
    .avatar-option {
        width: 80px;
        height: 80px;
    }
}

/* Form Styles */
.profile-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #ffffff;
    font-weight: 600;
}

.form-group input {
    width: 100%;
    padding: 0.8rem;
    background: var(--input-bg);
    border: 2px solid var(--muted-red);
    border-radius: 5px;
    color: #ffffff;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--blood-red);
}

/* Button Styles */
.btn {
    width: 100%;
    padding: 0.8rem;
    font-size: 1rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Crimson Text', serif;
    font-weight: 600;
    transition: all 0.3s ease;
}

@media (min-width: 768px) {
    .btn {
        width: auto;
        padding: 0.8rem 1.5rem;
    }
}

.btn-save {
    background: var(--blood-red);
    color: #ffffff;
    margin-top: 1rem;
}

.btn-save:hover {
    background: var(--muted-red);
    box-shadow: 0 0 15px var(--text-glow);
}

/* Stats Section */
.stats-section {
    grid-column: 1 / -1;
    padding: 1rem;
    margin-top: 1.5rem;
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid var(--blood-red);
    border-radius: 15px;
}

@media (min-width: 768px) {
    .stats-section {
        padding: 2rem;
        margin-top: 2rem;
    }
}

.stats-section h2 {
    font-family: 'Nosifer', cursive;
    color: var(--blood-red);
    text-shadow: 0 0 10px var(--text-glow);
    margin-bottom: 1.5rem;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

@media (min-width: 576px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 992px) {
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Stat Cards */
.stat-card {
    background: rgba(0, 0, 0, 0.7);
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
}

@media (min-width: 768px) {
    .stat-card {
        padding: 1.5rem;
    }
}

.stat-card.easy { border: 2px solid var(--easy-color); }
.stat-card.medium { border: 2px solid var(--medium-color); }
.stat-card.hard { border: 2px solid var(--hard-color); }

.stat-card h3 {
    font-family: 'Nosifer', cursive;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #ffffff;
}

.stat-card .total {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
}

/* Category Stats */
.category-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-top: 0.75rem;
}

.category-item {
    padding: 0.4rem;
    font-size: 0.9rem;
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.category-item span {
    display: block;
    font-size: 1.1rem;
}

@media (min-width: 768px) {
    .category-item {
        padding: 0.5rem;
        font-size: 1rem;
    }
}

/* Back Button */
.back-button {
    position: absolute;
    top: 1rem;
    left: 1rem;
    padding: 0.6rem 1.2rem;
    background: var(--blood-red);
    color: #ffffff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Crimson Text', serif;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.back-button:hover {
    background: var(--muted-red);
    box-shadow: 0 0 15px var(--text-glow);
    transform: translateX(-3px);
}

.back-button::before {
    content: '←';
    font-size: 1.2rem;
}

@media (max-width: 768px) {
    .back-button {
        position: static;
        margin: 1rem;
        width: calc(100% - 2rem);
        justify-content: center;
    }

    .container {
        margin-top: 0;
    }
}

/* Password Change Section */
.password-change-button {
    background: var(--blood-red);
    color: #ffffff;
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Crimson Text', serif;
    font-weight: 600;
    margin-top: 1rem;
    width: 100%;
    transition: all 0.3s ease;
}

.password-change-button:hover {
    background: var(--muted-red);
    box-shadow: 0 0 15px var(--text-glow);
}

.password-change-section {
    display: none;
    margin-top: 2rem;
    padding: 2rem;
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid var(--blood-red);
    border-radius: 10px;
}

.password-change-section.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Message Overlay */
.message-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 1rem;
}

.message-content {
    background: var(--dark-bg);
    border: 2px solid var(--blood-red);
    width: 90%;
    max-width: 400px;
    margin: 0 auto;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
}

