@import url('https://fonts.googleapis.com/css2?family=Nosifer&family=Merriweather:wght@400;700&display=swap');

:root {
    --dark-bg: #121212;
    --blood-red: #8B0000;
    --muted-red: #5D1E1E;
    --text-glow: rgba(139, 0, 0, 0.7);
}

body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 0;
    margin: 0;
    background-image: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url("../images/dark\ forest\ picture.jpg");
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    font-family: 'Merriweather', serif;
    color: #ffffff;
}

.container {
    width: 100%;
    max-width: 100%;
    padding: 0;
}

.row {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
}

.col-md-8 {
    max-width: 500px;
    width: 100%;
    padding: 0 15px;
}

body::after {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(139,0,0,0.1), rgba(0,0,0,0.5));
    animation: bg-shift 15s ease infinite;
    pointer-events: none;
    z-index: -1;
}

.login-container {
    width: 100%;
    max-width: 500px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 20px;
    box-shadow: 0 0 30px rgba(139, 0, 0, 0.5);
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 2px solid var(--blood-red);
}

.error-message {
    background-color: rgba(255, 0, 0, 0.2);
    color: #ff4500;
    border: 2px solid #ff4500;
    border-radius: 10px;
    padding: 10px;
    margin-bottom: 20px;
    width: 100%;
    text-align: center;
    display: none;
}

h1 {
    font-family: 'Nosifer', cursive;
    font-size: 2.5rem;
    margin-bottom: 20px;
    color: transparent; 
    -webkit-text-stroke: 2px var(--blood-red);
    text-shadow: 0 0 15px var(--text-glow);
    background-image: linear-gradient(45deg, var(--blood-red), #ff4500);
    -webkit-background-clip: text;
    background-clip: text;
    animation: blood-pulse 1s ease-in-out infinite alternate;
}

.login-form {
    width: 100%;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.form-control {
    margin-bottom: 15px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: 2px solid var(--blood-red);
    transition: all 0.3s ease;
}

.form-control::placeholder {
    color: rgba(255,255,255,0.6);
    font-family: 'Merriweather', serif;
}

.form-control:focus {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-color: #ff4500;
    box-shadow: 0 0 15px var(--text-glow);
    outline: none;
}

.btn-login {
    padding: 15px 25px;
    margin: 15px 0;
    width: 250px;
    border: none;
    border-radius: 10px;
    background-image: linear-gradient(to right, var(--blood-red), #5D1E1E);
    color: white;
    font-family: 'Nosifer', sans-serif;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(139, 0, 0, 0.5);
}

.btn-login:hover {
    transform: scale(1.05);
    box-shadow: 0 0 25px rgba(139, 0, 0, 0.7);
}

.login-links {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
    width: 100%;
}

.login-link {
    color: rgba(255,255,255,0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.login-link:hover {
    color: var(--blood-red);
    text-decoration: underline;
}

.btn-secondary {
    padding: 15px 25px;
    width: 250px;
    border: 2px solid var(--blood-red);
    background: transparent;
    color: white;
    font-family: 'Nosifer', sans-serif;
    font-size: 15px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(139, 0, 0, 0.3);
    margin: 10px 0;
}

.btn-secondary:hover {
    transform: scale(1.05);
    background: rgba(139, 0, 0, 0.3);
    box-shadow: 0 0 25px rgba(139, 0, 0, 0.5);
}

/* Modal Styles */
.modal-content {
    background: rgba(0, 0, 0, 0.9);
    border: 3px solid var(--blood-red);
    border-radius: 15px;
    color: white;
}

.modal-header {
    border-bottom: 1px solid rgba(139, 0, 0, 0.3);
    padding: 1rem;
}

.modal-title {
    font-family: 'Nosifer', cursive;
    color: var(--blood-red);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(139, 0, 0, 0.3);
    padding: 1rem;
    justify-content: center;
}

.btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

@media (max-width: 768px) {
    body {
        margin: 5%;
        margin-top: 2%;
    }

    h1 {
        font-size: 2rem;
    }

    .login-container {
        padding: 20px;
    }

    .btn-login, .btn-secondary {
        width: 100%;
    }
}
/* Mobile Responsiveness for Forgot Password Modal */
@media (max-width: 576px) {
.modal-dialog {
margin: 1.75rem 0.5rem;
max-width: calc(100% - 1rem);
}

.modal-content {
border-radius: 10px;
}

.modal-header {
padding: 0.75rem;
align-items: center;
}

.modal-title {
font-size: 1.2rem;
}

.btn-close {
padding: 0.5rem;
margin: -0.5rem -0.5rem -0.5rem auto;
width: 1rem;
height: 1rem;
}

.modal-body {
padding: 1rem;
}

.form-control {
font-size: 0.9rem;
padding: 0.625rem 0.75rem;
}

.modal-footer {
padding: 0.75rem;
flex-direction: column;
gap: 10px;
}

.btn-login {
width: 100%;
padding: 10px 15px;
font-size: 0.9rem;
}

.text-muted {
font-size: 0.8rem;
margin-top: 0.5rem;
}
}

/* Ensure modal is fully responsive on very small screens */
@media (max-width: 375px) {
.modal-dialog {
margin: 1rem 0.25rem;
}

.modal-content {
border-radius: 8px;
}

.modal-header {
padding: 0.5rem;
}

.modal-title {
font-size: 1rem;
}

.modal-body {
padding: 0.75rem;
}

.form-control {
font-size: 0.8rem;
padding: 0.5rem 0.625rem;
}
}

/* Existing animations remain the same */
@keyframes blood-pulse {
    0% {
        text-shadow: 0 0 10px var(--text-glow);
        transform: scale(1);
    }
    100% {
        text-shadow: 0 0 25px var(--text-glow);
        transform: scale(1.03);
    }
}

@keyframes bg-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}
/* Add these styles to your existing CSS file */

.social-login {
    width: 100%;
    margin: 20px 0;
}

.divider {
    width: 100%;
    text-align: center;
    border-bottom: 1px solid rgba(139, 0, 0, 0.3);
    line-height: 0.1em;
    margin: 20px 0;
}

.divider span {
    background: rgba(0, 0, 0, 0.6);
    padding: 0 10px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.social-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.btn-social {
    width: 120px;
    padding: 10px;
    border: 2px solid var(--blood-red);
    background: rgba(0, 0, 0, 0.5);
    color: white;
    font-family: 'Merriweather', serif;
    font-size: 14px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-google:hover {
    background: rgba(219, 68, 55, 0.2);
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(219, 68, 55, 0.3);
}

.btn-facebook:hover {
    background: rgba(66, 103, 178, 0.2);
    transform: scale(1.05);
    box-shadow: 0 0 15px rgba(66, 103, 178, 0.3);
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .social-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-social {
        width: 200px;
    }
}