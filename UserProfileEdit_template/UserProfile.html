<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Realm - Profile Settings</title>
    <link href="https://fonts.googleapis.com/css2?family=Nosifer&family=Crimson+Text:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../css/UserProfile.css">
</head>
<body>
    <button class="back-button" id="backbtn">Back to Game</button>
    
    <div class="container">
        <div class="profile-header">
            <h1>Dark Realm Profile</h1>
        </div>

        <div class="profile-grid">
            <div class="avatar-section">
                <div class="current-avatar">
                    <img id="current-avatar-img" src="https://api.dicebear.com/7.x/adventurer/svg?seed=1" alt="Current Avatar">
                </div>
                <h2>Choose Your Avatar</h2>
                <input  type="hidden" value="{{.UserAvatar}}" id="avatartype">
                <div class="avatar-gallery" id="avatar-gallery">
                    <!-- Avatar options will be loaded here -->
                </div>
            </div>

            <div class="profile-details">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" value="{{.Username}}" required>
                </div>

                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" value="{{.Useremail}}" required>
                </div>

                <button class="btn btn-save" id="save-profile">Save Changes</button>
                
                <button class="password-change-button" id="show-password-form">Change Password</button>
                
                <div class="password-change-section" id="password-section">
                    <h3>Change Password</h3>
                    <div class="form-group">
                        <label for="current-password">Current Password</label>
                        <input type="password" id="current-password" placeholder="Enter current password" required>
                    </div>

                    <div class="form-group">
                        <label for="new-password">New Password</label>
                        <input type="password" id="new-password" placeholder="Enter new password" required>
                    </div>

                    <div class="form-group">
                        <label for="confirm-password">Confirm New Password</label>
                        <input type="password" id="confirm-password" placeholder="Confirm new password" required>
                    </div>

                    <button class="btn btn-save" id="save-password">Update Password</button>
                </div>
            </div>

            <!-- Stats Section remains the same -->
            <div class="stats-section">
                <h2>Riddle Solving Stats</h2>
                <div class="stats-grid">
                    <div class="stat-card easy">
                        <h3>Easy Mode</h3>
                        <div class="total">Total Solved: {{.EasyStat.Total}}</div>
                        <div class="category-stats">
                            <div class="category-item">
                                <span>🧮 Math</span>
                                <span>{{.EasyStat.Mathematic}}</span>
                            </div>
                            <div class="category-item">
                                <span>📝 Word</span>
                                <span>{{.EasyStat.Word}}</span>
                            </div>
                            <div class="category-item">
                                <span>🤔 Logic</span>
                                <span>{{.EasyStat.Logic}}</span>
                            </div>
                            <div class="category-item">
                                <span>🌐 General</span>
                                <span>{{.EasyStat.General}}</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card medium">
                        <h3>Medium Mode</h3>
                        <div class="total">Total Solved: {{.MediumStat.Total}}</div>
                        <div class="category-stats">
                            <div class="category-item">
                                <span>🧮 Math</span>
                                <span>{{.MediumStat.Mathematic}}</span>
                            </div>
                            <div class="category-item">
                                <span>📝 Word</span>
                                <span>{{.MediumStat.Word}}</span>
                            </div>
                            <div class="category-item">
                                <span>🤔 Logic</span>
                                <span>{{.MediumStat.Logic}}</span>
                            </div>
                            <div class="category-item">
                                <span>🌐 General</span>
                                <span>{{.MediumStat.General}}</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card hard">
                        <h3>Hard Mode</h3>
                        <div class="total">Total Solved: {{.HardStat.Total}}</div>
                        <div class="category-stats">
                            <div class="category-item">
                                <span>🧮 Math</span>
                                <span>{{.HardStat.Mathematic}}</span>
                            </div>
                            <div class="category-item">
                                <span>📝 Word</span>
                                <span>{{.HardStat.Word}}</span>
                            </div>
                            <div class="category-item">
                                <span>🤔 Logic</span>
                                <span>{{.HardStat.Logic}}</span>
                            </div>
                            <div class="category-item">
                                <span>🌐 General</span>
                                <span>{{.HardStat.General}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="message-overlay" id="message-overlay">
        <div class="message-content">
            <h2 id="message-title"></h2>
            <p id="message-text"></p>
            <button class="btn btn-save" onclick="closeMessage()">Close</button>
        </div>
    </div>

   <script defer src="../js/UserProfile.js"></script>
</body>
</html>