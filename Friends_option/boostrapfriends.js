//!START OF RIDDLE ARRAYS AND ANSWER ARRYS
var riddle1 = [
  "How many months have 28 days?",
  "What nevers ask questions but is always answered?",
  "I am an odd number,take away a letter I become even.What number am I?",
  "What has hands but cannot clap?",
  "What is easy to get into but hard to get out?",
  "I am tall when I am young but am short when I am old who am I",
  "What is full hole but still holds water",
  "Which is heavier a kilo of stone or a kilo of featther",
  "What is at the end of rainbow",
];
var riddle2 = [
  "What three letter changes a girl to a woman?",
  "What can you easily break but you can never touch it?",
  "what question can you ask where you can get different answers every time but all the answers being correct?",
  "I am first on earth,second in heaven.I appear twice in a week,never in a month,but once ina year.What am I?",
  "What's in the beginning and the end,but not not in the middle?",
  "When can you add 2 to 11 and get 1 as the answer?",
  "Say my name and I am no more?",
  "What can you hold in your right hand but never in your left hand?",
  "What can be larger than you without weighing anything?",
];
var riddle3 = [
  "What do Henry the Eighth and Kermit the Frog have in common?",
  "What is the only word that is spelled wrong in the dictionary",
  "I am in life but not in death.You can't have fun without me.What am I?",
  "What does everyone know how to open but not how to close?",
  "I start with E end with with E.I have whole countries inside me.What am I?",
  "I never speak unless spoken to,many have heard me but none have seen me.What am I?",
  "What can fill an entire room without taking up any space?",
];
var riddle4 = [
  "I speak without a mouth and hears without ears.I have no body,but I come alive with wind.What am I?",
  "You measure my life in hours and I serve you by expiring.I am quick when I am thin and slow when I am fat.The wind is my enemy",
  "Three different doctors said that Paul is their brother yet Paul calims he has no brothers.Who is lying",
  "How many letters are in the alphabet?",
  "What gets wet when drying?",
  "I have cities,but no houses.I have mountains,but no trees.I have water,but no fish.What am I?",
  "you see a boat filled with people.It has not sunk,but when you look again you don't see a single person on the boat.Why?",
  "Mr.Taylor has four daoughters and each has a brother.In total,how many children does Mr.Taylor have?",
];
var riddle5 = [
  "What can run but never walk,have a mouth but never talk,have a head that never weeps,and a bed that nevers sleeps?",
  "If an electric train is moving north at 55mph and the winds blowing eastt at 70mph,which way does the smoke blow?",
  "There are 6 sisters.Each sister has 1 brother.How many brothers are in the sister's family?",
  "Two fathers and two sons come home from the mall.Yet when they arrive hoeme,only three people get out of the car.How is this possible?",
  "A murder is condemned to death and he has the option to die in one of the the following three rooms:A room full of raging fire,a room full of assassins with loaded guns and a room full of lions who haven't eaten in years.Which room should he choose?",
];
var riddle6 = [
  "If you eat me,my sender will eat you.What am I?",
  "Steve was murdered on saturday afternoon.His wife said she was reading.The doorman said he was in the shower.The chef said he was making breakfast.The gardener was pruning hedges.From the information given,who committed the murder?",
  "If a plane came crashing down on the border between Canada and America,where are the survivors buried?",
];
var riddle7 = [
  "The more these are taken,the more they are left behind.what are they?",
  "I eat to live and drink to die.What could I be?",
  "What is caught but never thrown?",
  "Humans purchase me to eat but then never eat me.what am I?",
  "What is more useful when it is broken?",
  "I am easy to lift, but hard to throw. What am I?",
  " What goes up, but never comes down?",
  "A bus driver goes the wrong way down a one-way street. He passes the cops, but they do not stop him. Why?",
];
var riddle8 = [
  "I am a five-letter word and people eat me.If you remove the first letter I  become an energy form.If you remove the first two letters,I am needed to live.Scramble the lat three letters an and I am a drink.What word am I?",
  "What do we call a woman who always know her husband whereabouts all the time.",
  'A Woman is sittin in her hotel room when someone knocks at the door.she opened the door to find a man she\'s never seen before.He says,"Oh I am sorry,I made a mistake and thought this was my room".He then ran awy and got into an elevator so the woman shut her door and phoned security.What made the woman so suspicious?',
];
var riddle9 = [
  "Rachel goes to the supermarket and buys 10 tomatoes. Unfortunately, on the way back home, all but 9 get ruined. How many tomatoes are left in a good condition?",
  "If you multiply this number by any other number, the answer will always be the same. What number is this?",
  "Two girls were born to the same mother, on the same day, at the same time, in the same month and year, and yet they're not twins. How can this be?",
  "I move very slowly at an imperceptible rate, although I take my time, I am never late. I accompany life and survive past demise; I am viewed with esteem in many women's eyes. What am I?",
];
var riddle10 = [
  "Who has married many women but was never married?",
  "What does a man do only once in his lifetime, but women do once a year after they are 29?",
  "Poor people have it. Rich people do not need it. If you eat it you die. What is it?",
  "They come out at night without being called, and are lost in the day without being stolen. What are they?",
  "What is always in front of you, but cannot be seen?",
  "What goes through cities and fields, but never moves?",
  "What 2 things can you never eat for breakfast?",
  "What is always on its way but never arrives?",
  "What word in the English language fulfils all the following criteria: The first two letters signify a male, the first three letters signify a female, the first four letters signify a great person, while the entire word signifies a great woman. ",
];
var riddle11 = [
  "What English word retains the same pronunciation, even after you take away four of its five letters?",
  "When John was six years old he hammered a nail into his favorite tree to mark his height. Ten years later at age sixteen, John returned to see how much higher the nail was. If the tree grew by five centimeters each year, how much higher would the nail be?",
  "I am four times as old as my daughter. In 20 years time I shall be twice as old as her. How old are we now?",
  " Lily is a lilypad in a small pond. Lilly doubles her size each day, On the 20th day she covers the whole pond. On what day was Lily half the size of the pond?",
  "I am something people love or hate. I change people  appearances and thoughts. If a person takes care of themself, I will go up even higher. To some people, I will fool them. To others, I am a mystery. Some people might want to try and hide me, but I will show. No matter how hard people try, I will never go down. What am I?",
  "If you have me, you want to share me. If you share me, you don't have me.",
];
var riddle12 = [
  " How can you drop a raw egg from a height onto a concrete floor without cracking it?",
  "What tastes better than it smells?",
  "What building has the most stories?",
  " What kind of tree can you carry in your hand?",
  "What starts with T, ends with T, and has T in it?",
  " Where is the only place where today comes before yesterday?",
  "What comes at the end of everything?",
  ' What do the letter "t" and an island have in common?',
  " What kind of ship has two mates but no captain?",
];
var riddle13 = [
  " If you throw a blue stone into the Red Sea, what will it become?",
  "What gets shorter as it grows older?",
  "What five-letter word becomes shorter when you add two letters to it?",
  "What is always found on the ground but never gets dirty?",
  "What has a head and a tail but no body?",
  "What has many teeth but cannot bite?",
  "What has one head, one foot, and four legs?",
  "What gets smaller every time it takes a bath?",
  "What 5-letter word typed in all capital letters can be read the same upside down?",
  "What do you bury when it's alive and dig up when it's dead?",
];
var riddle14 = [
  "What is it that we burry and it comes back to life?",
  'When I say,"You are staggering my son,"which animals am I talking about?',
  " What is taken before you can get it?",
  "What belongs to you but is used by everyone you meet?",
  "What is lighter than a feather but impossible to hold for much more than a minute?",
  " A cowboy rides into town on Friday. He stays three days, then rides out of town on Friday. How?",
  "If your uncle's sister is not your aunt, then who is she to you?",
  " Two people are born at the same moment, but they don't have the same birthdays. How?",
];
var riddle15 = [
  " The person who made it does not need it. The person who bought it does not want it. The person who needs it does not know it. What is it?",
  "A man goes outside in the rain without an umbrella or hat but doesn't get a single hair on his head wet. How?",
  "If you are running a race and pass the person in second, then what place are you in?",
  "You enter a room that contains a match, kerosene lamp, candle, and fireplace. What should you light first?",
  " A mother and father have four daughters, and each daughter has one brother. How many people are in the family?",
  " If the day before yesterday was the 23rd, then what will be the day after tomorrow?",
  "What common English verb becomes its own past tense by rearranging its letters?",
  "A is B's father but B isn't A's son. How?",
  "What is one thing that all people, regardless of their politics or religion, have to agree is between heaven and earth?",
];
var riddles = [
  ...riddle1,
  ...riddle2,
  ...riddle3,
  ...riddle4,
  ...riddle5,
  ...riddle6,
  ...riddle7,
  ...riddle8,
  ...riddle9,
  ...riddle10,
  ...riddle11,
  ...riddle12,
  ...riddle13,
  ...riddle14,
  ...riddle15,
];

//arrays for answers
var answer1 = [
  /all|every month|all months/gi,
  /doorbell/gi,
  /seven|7/gi,
  /clock/gi,
  /trouble/gi,
  /candle/gi,
  /sponge/gi,
  /none|all weigh the same/gi,
  /^w{1}$/gi,
];
var answer2 = [
  /age/gi,
  /promise/gi,
  /what time is it|what is the clock|time/gi,
  /^e{1}$|letter e/gi,
  /^n{1}$|letter n/gi,
  /when you add 2 hours to 11 o'clock|in a clock|time/gi,
  /silence/gi,
  /my lefthand|left hand|your lefthand|lefthand/gi,
  /shadow/gi,
];
var answer3 = [
  /middle name/gi,
  /wrong/gi,
  /letter f|^f{1}$/gi,
  /egg/gi,
  /Europe/gi,
  /Echo/gi,
  /light/gi,
];
var answer4 = [
  /echo/gi,
  /candle/gi,
  /no one|none/gi,
  /eleven|11/gi,
  /towel/gi,
  /map/gi,
  /marrried|couples/gi,
  /five|5/gi,
];
var answer5 = [
  /river/gi,
  /don,t smoke|no smoke/gi,
  /one|1/gi,
  /grandfather,father and son|grandfather,son,father|grandfather,father and grandson/gi,
  /a room full of lions|lions/gi,
];
var answer6 = [/fishhook/gi, /chef/gi, /survivors are not buried|neither/gi];
var answer7 = [
  /footsteps|footsteps/gi,
  /fire/gi,
  /a cold|cold/gi,
  /plate|spoon/gi,
  /egg/gi,
  /feather|feathers/gi,
  /age/gi,
  /walking|on foot/gi,
];
var answer8 = [/wheat/gi, /widow/gi, /don't knock|never knock/gi];
var answer9 = [/nine|9/gi, /zero|0/gi, /triplet/gi, /hair/gi];
var answer10 = [
  /priest/gi,
  /turn 30/gi,
  /nothing/gi,
  /stars|star/gi,
  /future/gi,
  /road|roads/gi,
  /lunch|dinner|supper/gi,
  /tomorrow/gi,
  /heroine/gi,
];
var answer11 = [
  /queue/gi,
  /same|did not change/gi,
  /40|10/gi,
  /19|nineteen/gi,
  /age/gi,
  /secret/gi,
];
var answer12 = [
  /hard|concrete floor are hard to crack/gi,
  /tongue/gi,
  /library/gi,
  /palm/gi,
  /teapot/gi,
  /dictionary/gi,
  /letter g|^g{1}$/gi,
  /middle of water/gi,
  /relationship/gi,
];
var answer13 = [
  /wet/gi,
  /candle/gi,
  /short/gi,
  /shadow/gi,
  /coin/gi,
  /comb/gi,
  /bed/gi,
  /soap/gi,
  /swims/gi,
  /plant/gi,
];
var answer14 = [
  /seed/gi,
  /chameleon/gi,
  /picture/gi,
  /name/gi,
  /breathe/gi,
  /named friday|called friday|horse named friday/gi,
  /mother/gi,
  /timezone/gi,
];
var answer15 = [
  /coffin/gi,
  /bald/gi,
  /second|2/gi,
  /match/gi,
  /seven|7/gi,
  /27th|27/gi,
  /eat/gi,
  /daughter/gi,
  /and/gi,
];
var answers = [
  ...answer1,
  ...answer2,
  ...answer3,
  ...answer4,
  ...answer5,
  ...answer6,
  ...answer7,
  ...answer8,
  ...answer9,
  ...answer10,
  ...answer11,
  ...answer12,
  ...answer13,
  ...answer14,
  ...answer15,
];

//!END OF RIDDLE ARRAYS AND ANSWER ARRAYS

//!START OF FORM
//!CREATING THE INPUT ELEMENTS //  ADDING A NEW PLAYER
var i = 0;
function count() {
  i++;
}

function inputtext() {
  var divs = document.createElement("div");
  divs.classList.add("mt-3");

  var placeholders = [
    "PLAYER THREE",
    "PLAYER FOUR",
    "PLAYER FIVE",
    "PLAYER SIX",
  ];

  var input = document.createElement("input");
  input.type = "text";
  input.classList.add("form-control");
  input.classList.add("playeers");
  input.classList.add("text-info");
  input.placeholder = placeholders[i];

  divs.appendChild(input);

  var contaiiner = document.getElementById("playernamess");
  return contaiiner.appendChild(divs);
}

var alerts = document.getElementById("alert");
var alertmessage = document.getElementById("alertmessage");

//*THIS FUNCTION ADDS CLASS REMOVE WHEN IS CALLED IF IT FINDS THE ALERT ELEMENT HAS NO CLASS REMOVE
function checkremove() {
  if (!alerts.classList.contains("remove")) {
    alerts.classList.add("remove");
  }
}

//*THIS FUNCTION IS CHARGE OF CHECKING HOW MANY PLAYERS ARE IN THE GAME (IT MAKES SURE ONLY SIX PLAYERS CAN PLAY THE GAME)
//*THE FUNCTION CALLS THE CHECKREMOVE TO ADD CLASS REMOVE INCASE IF IT HAD BEEN REMOVED EALIER BY OTHER FUNCTION
//*THE FUNCTION USES PLAYEERS CLASS TO KNOW THE NUMBER OF INPUT
function checck() {
  checkremove();
  var z = [...document.querySelectorAll(".playeers")];
  if (z.length > 5) {
    alerts.classList.remove("remove");
    alertmessage.textContent = "MAXIMUM NUMBER OF PLAYERS REACHED";
  } else {
    inputtext();
    count();
  }
}

//*THE FUNCTION DELETES A PLAYER
//*WE GET THE PARENT CONTAINER THEN DELETE THE LAST ELEMENT TO DELETE THE LAST PLAYER INPUT
function deleteplayer() {
  checkremove();
  var z = [...document.querySelectorAll(".playeers")];
  if (z.length > 2) {
    var contaiiner = document.getElementById("playernamess");
    var lastElement = contaiiner.lastElementChild;
    lastElement.remove();
  } else {
    alerts.classList.remove("remove");
    alertmessage.textContent = "THE MINIMUM NUMBER OF PLAYERS IS TWO";
  }
}

var button = document.getElementById("addplayers");
var buttons = document.getElementById("deleteplayers");
//!END OF CREATING NEW PLAYER  AND DELETING PLAYER module

//!CHECKING TO ENSURE THAT ALL INPUT ARE FILLED
function checkinput() {
  checkremove();

  var z = [...document.querySelectorAll(".playeers")];
  z.every(ted);
  function ted(item) {
    return item.value != "";
  }

  if (z.every(ted)) {
    proceedToRiddleDisplay();
  } else {
    alerts.classList.remove("remove");
    alertmessage.textContent = "LET ALL PLAYERS FILL THEIR NAMES";
  }
}
//!END OF CHECKING INPUT ARE FILLED

var butt = document.getElementById("batt"); ///BATTTLE BUTTON

//!FUNCTION TO PROCEED TO THE RIDDLE DISPLAY()
function proceedToRiddleDisplay() {
  var playformre = document.getElementById("playformreg");
  playformre.classList.add("remove");

  var headsectiom = document.getElementById("headsection");
  headsectiom.classList.remove("remove");

  var playersection = document.getElementById("playsection");
  playersection.classList.remove("remove");
}
//!END OF THE FUNCTION TO PROCEED TO RIDDLE DISPLAY()

//!END OF FORM

//!START OF HEADER SECTION*/

//?START OF FUNCTION TO CREATE  PROGRESS BAR FOR OTHER PLAYERS
var f = 0;
function ren() {
  return f++;
}

var ids = ["progress3", "progress4", "progress5", "progress6"];
var pids = ["player13", "player14", "player15", "player16"];

function modalprogressbar() {
  var containerbox = document.getElementById("modall");

  var conta = document.createElement("div");
  conta.classList.add("progress");
  conta.classList.add("mt-2");
  conta.style.height = "20%";
  containerbox.appendChild(conta);

  var pross = document.createElement("div");
  pross.classList.add("progress-bar");
  pross.classList.add("progress-bar-striped");
  pross.classList.add("progress-bar-animated");
  pross.role = "progressbar";
  pross.id = ids[f];
  conta.appendChild(pross);

  var tex = document.createElement("p");
  tex.classList.add("text-danger");
  tex.classList.add("fs-4");
  pross.appendChild(tex);

  var pname = document.createElement("p");
  pname.classList.add("text-danger");
  pname.id = pids[f];
  containerbox.appendChild(pname);

  ren();
}
function progressbars() {
  var p = [...document.querySelectorAll(".progress")];
  if (p.length > 5) {
    alerts.classList.remove("remove");
    alertmessage.textContent = "MAXIMUM NUMBER OF PLAYERS REACHED";
  } else {
    modalprogressbar();
  }
}

let progressbarss;
function numberOfProgressBar() {
  return (progressbarss = [...document.querySelectorAll(".progress")]);
}

function deleteProgressbars() {
  numberOfProgressBar();
  if (progressbarss.length > 2) {
    var containerbox = document.getElementById("modall");
    containerbox.lastElementChild.remove();

    var text = [...document.querySelectorAll(".progress")];
    text.pop();
  } else {
    //
  }
}

function dleteProgressbarr() {
  deleteProgressbars();
  deleteProgressbars();
}
//?END OF FUNCTION TO CREATE PROGRESS BAR

//?START OF NAME AND SCORES DISPLAYS(ADD SCORE FUNCTION/CHANGE PLAYER FUNCTION)
let players = [];
let playerscores = [];

function numberOfplayers() {
  var z = [...document.querySelectorAll(".playeers")];
  playerscores = new Array(z.length).fill(0);

  return (players = z.map((item) => {
    return item.value;
  }));
}

let playerindex = 0;
let currentPlayer = players[playerindex];
let currentscore = playerscores[playerindex];

function displayPlayers() {
  currentPlayer = players[playerindex];
  currentscore = playerscores[playerindex];
  document.getElementById("playername").innerHTML = currentPlayer.toUpperCase();
  document.getElementById("score").innerHTML = ":" + currentscore;

  //*Modal
  if (players.length == 2) {
    document.getElementById("playerone10").innerHTML = players[0].toUpperCase();
    document.getElementById("playerone12").innerHTML = players[1].toUpperCase();
  } else if (players.length == 3) {
    document.getElementById("playerone10").innerHTML = players[0].toUpperCase();
    document.getElementById("playerone12").innerHTML = players[1].toUpperCase();
    document.getElementById(pids[0]).innerHTML = players[2].toUpperCase();
  } else if (players.length == 4) {
    document.getElementById("playerone10").innerHTML = players[0].toUpperCase();
    document.getElementById("playerone12").innerHTML = players[1].toUpperCase();
    document.getElementById(pids[0]).innerHTML = players[2].toUpperCase();
    document.getElementById(pids[1]).innerHTML = players[3].toUpperCase();
  } else if (players.length == 5) {
    document.getElementById("playerone10").innerHTML = players[0].toUpperCase();
    document.getElementById("playerone12").innerHTML = players[1].toUpperCase();
    document.getElementById(pids[0]).innerHTML = players[2].toUpperCase();
    document.getElementById(pids[1]).innerHTML = players[3].toUpperCase();
    document.getElementById(pids[2]).innerHTML = players[4].toUpperCase();
  } else {
    document.getElementById("playerone10").innerHTML = players[0].toUpperCase();
    document.getElementById("playerone12").innerHTML = players[1].toUpperCase();
    document.getElementById(pids[0]).innerHTML = players[2].toUpperCase();
    document.getElementById(pids[1]).innerHTML = players[3].toUpperCase();
    document.getElementById(pids[2]).innerHTML = players[4].toUpperCase();
    document.getElementById(pids[3]).innerHTML = players[5].toUpperCase();
  }
}

function changePlayer() {
  playerindex++;
  if (playerindex > players.length - 1) {
    playerindex = 0;
    currentPlayer = players[playerindex];
    currentscore = playerscores[playerindex];
  } else {
    currentPlayer = players[playerindex];
    currentscore = playerscores[playerindex];
  }
  document.getElementById("playername").innerHTML = currentPlayer.toUpperCase();
  document.getElementById("score").innerHTML = ":" + currentscore;
}

function scoreadder() {
  if ((currentPlayer = players[playerindex])) {
    playerscores[playerindex] += 10;
    currentscore = playerscores[playerindex];
  }
  document.getElementById("score").innerHTML = ":" + currentscore;

  //*MODAL
  if (players.length == 2) {
    let scoring = playerscores[0].toString() + "%";
    let scoring2 = playerscores[1].toString() + "%";

    document.getElementById("progress1").style.width = scoring;
    document.getElementById("progress2").style.width = scoring2;
  } else if (players.length == 3) {
    let scoring = playerscores[0].toString() + "%";
    let scoring2 = playerscores[1].toString() + "%";
    let scoring3 = playerscores[2].toString() + "%";

    document.getElementById("progress1").style.width = scoring;
    document.getElementById("progress2").style.width = scoring2;
    document.getElementById(ids[0]).style.width = scoring3;
  } else if (players.length == 4) {
    let scoring = playerscores[0].toString() + "%";
    let scoring2 = playerscores[1].toString() + "%";
    let scoring3 = playerscores[2].toString() + "%";
    let scoring4 = playerscores[3].toString() + "%";

    document.getElementById("progress1").style.width = scoring;
    document.getElementById("progress2").style.width = scoring2;
    document.getElementById(ids[0]).style.width = scoring3;
    document.getElementById(ids[1]).style.width = scoring4;
  } else if (players.length == 5) {
    let scoring = playerscores[0].toString() + "%";
    let scoring2 = playerscores[1].toString() + "%";
    let scoring3 = playerscores[2].toString() + "%";
    let scoring4 = playerscores[3].toString() + "%";
    let scoring5 = playerscores[4].toString() + "%";

    document.getElementById("progress1").style.width = scoring;
    document.getElementById("progress2").style.width = scoring2;
    document.getElementById(ids[0]).style.width = scoring3;
    document.getElementById(ids[1]).style.width = scoring4;
    document.getElementById(ids[2]).style.width = scoring5;
  } else {
    let scoring = playerscores[0].toString() + "%";
    let scoring2 = playerscores[1].toString() + "%";
    let scoring3 = playerscores[2].toString() + "%";
    let scoring4 = playerscores[3].toString() + "%";
    let scoring5 = playerscores[4].toString() + "%";
    let scoring6 = playerscores[5].toString() + "%";

    document.getElementById("progress1").style.width = scoring;
    document.getElementById("progress2").style.width = scoring2;
    document.getElementById(ids[0]).style.width = scoring3;
    document.getElementById(ids[1]).style.width = scoring4;
    document.getElementById(ids[2]).style.width = scoring5;
    document.getElementById(ids[3]).style.width = scoring6;
  }
}
//?END OF NAME AND SCORES DISPLAYS(ADD SCORE FUNCTION/CHANGE PLAYER FUNCTION)

//!END OF HEADER SECTION

//!START OF RIDDLE DISPLAY
/**
 * *THIS FUNCTION IS USED TO GENERATE A RANDOM NUMBER INDEX,THE NUMBER GENERATED WILL BE CHECKED IF IT EXIST IN THE Z ARRAY
 * *IF THE RANDOM NUMBER EXIST IN THE Z ARRAY,ANOTHER NUMBER IS GNERATED,THE NUMBER THAT DO NOT EXIST IN THE Z ARRAY WILL RETURNED AS INDEXED
 * *ALSO THE NUMBER WILL BE PUT IN THE Z ARRAY,SO AS TO PREVENT THE NUMBER FROM BEING PICKED AGAIN.
 *
 * !THE Z ARRAY ALSO WILL BE USED IN THE GAMEOVER FUNCTION,THE GAMEOVER FUNCTION IS A FUNCTION IS CALLED TO SHOW THE GAME HAS ENDED
 */

let z = [];
function random() {
  do {
    var index = Math.floor(Math.random() * riddles.length);
  } while (z.indexOf(index) > -1);
  z.unshift(index);

  return index;
}

/**
 ** THIS IS IN CHARGE OF DISPLAYING THE RIDDLE TO THE PLAYER ,IT PICK A RANDOM NUMBER BY CALLING THE RANDOM FUNCTION TO GENERATE A RANDOM NUMBER
 * * WHICH WILL USED AS INDEX IN THE RIDDLE ARRAY TO PICK A RIDDLE.
 *
 * !THE RANDOMANSWER VARIABLE IS USED TO SHOW THE RANDOM NUMBER OR INDEX OF THE RIDDLE PICKED SO THAT IT WILL USED IN CHECKING FOR
 * !ANSWER FOR THAT SINCE THE RIDDLE ARRAY AND ANSWER ARRAY ARE PARALLEL TO EACH OTHER
 */

var randomanswer;
var Driddle = document.getElementById("riddle");

function displayRiddle(number) {
  Driddle.innerHTML = riddles[number];
  randomanswer = number;
}

/**
 **THIS FUNCTION IS CALLED WHEN A USER GETS THE RIDDLE RIGHT IT TO APPLAUSE THE USER FOR GETTING IT RIGHT
 */

var y = document.getElementById("correctdisplay");
function displaycorrect() {
  var y = document.getElementById("correctdisplay");
  y.style.color = "green";
  var teds = [
    "CORRECT!",
    "YOU NAILED IT!",
    "SPOT ON!",
    "SUPERB!",
    "KEEP IT UP",
  ];
  let t = Math.floor(Math.random() * teds.length);
  return (y.textContent = teds[t]);
}

//*THIS FUNCTION DELETE THE DISPLAYCORRECT FUNCTION WHEN A NEW RIDDLE IS DISPLAYED (WHEN USER CLICK NEXT BUTTON)

function delit() {
  return (y.textContent = "");
}

/***
 * *THIS FUNCTION IS USED TO GET THE USER ANSWER FOR A RIDDLE
 */

function getUserInput() {
  var userInput = document.getElementById("useranswer").value;
  return userInput;
}

/******
 **THIS FUNCTION IS USED TO CLEAR THE INPUT BOX WHEN THE USER CLICK NEXT FOR THE NEXT RIDDLE
 *****/

var userInput = document.getElementById("useranswer");
function clearresponse() {
  return (userInput.value = "");
}
/**
 **THIS IS THE CHECKIT FUNCTION IT IS IN CHARGE OF CHECKING IF THE USER ANSWER IS RIGHT,IF IT IS RIGHT IT CALLS SCORE FUNCTION TO ADD THE SCORE
 * *AND DISPLAYCORRECT TO GENERATE A RANDOM WORD OF TELLING THE PLAYER HIS ANSWER IS CORRECT
 * !THE RANDOMANSWER VARIABLE IS USED AS ARGUEMENT AS TO SHOW THE INDEX OF THE ANSWER REMEMBER RIDDLE AND ANSWER ARRAY ARE PARALLEL MEANING THE INDEX OF A RIDDLE IS THE SAME AS THE INDEX OF ITS ANSWER.
 *
 */

function checkit(answer) {
  if (answers[randomanswer].test(answer)) {
    scoreadder();
    displaycorrect();
    setTimeout(changePlayer, 4000); //changes players
  } else {
    todDisplay();
  }
}

/**********************
 *?WE PUT THE CHECKIT FUNCTION IN THIS ONLYONCE FUNCTION SO THAT IT CAN ONLY BE CALLED ONLY ONCE PER RIDDLE
 *?THE ONLYONCE FUNCTION USES THE VARIABLE CHECKONCE AS FLAG IN THAT WHEN IT CALLED IT CHECKS IF THE CHECKONCE VARIABLE IF IT IS FALSE OR TRUE,
 *?WHEN IT IS FALSE IT NOW CALLS THE CHECKIT FUNCTION TO DO IT THING ,IT ALSO SET THE CHECKONCE VARIABLE TO TRUE SO THAT WHEN IT IS TRIED TO BE CALLED IT WILL
 *?FIND THE CHECKONCE VARIABLE AS TRUE THUS IT WON'T THE CHECKIT  FUNCTION AGAIN MAKING THE CHECKIT FUNCTION TO BE CALLED ONLY ONCE.
 ****************/

let checkonce = false; //this is a flag used to call a function
function onlyonce() {
  if (!checkonce) {
    checkit(getUserInput());
    checkonce = true;
  }
}

/************
 ** THIS FUNCTION IS IN CHARGE WHEN THE PLAYER CLICKS  NEXT BUTTON IT RESET THE CHECKONCE VARIABLE TO FALSE TO ENABLE THE CHECKIT FUNCTION TO BE ONCE AGAIN FOR A NEW RIDDLE
 *************/

function resetcheckonce() {
  return (checkonce = false);
}
var submit = document.getElementById("submit");

//!END OF RIDDLE DISPLAY

/*******************************
 * !START OF TRUTH OR DARE SECTION
 * !START OF TRUTH OR DARE  SECTION
 * !START OF TRUTH OR DARE SECTION
 *****************************/
//*TOD CHECKER TO AVOID COLLISION BETWEEN TOD SECTION AND GAMEOVER SECTION
let checkeToD=true;

//*DISPLAYS TRUTH OR FALSE FOR THE USER TO USE.
function todDisplay() {
  if(checkeToD){
  var toddare = document.getElementById("tod");
  toddare.classList.remove("remove");

  var playersection = document.getElementById("playsection");
  playersection.classList.add("remove");

  var truth = document.getElementById("truth");
  truth.classList.remove("disabled");

  var dare = document.getElementById("dare");
  dare.classList.remove("disabled");
  }
}




var truTH = false; //*FLAG

//*FUNCTION FOR TRUTH THE DISPLAY IT SET truTH TO TRUE WHEN CALLED SO AS THE TRUTH SCORE ADDER FUNCTION WILL BE USED
function truthDisplay() {
  var toddare = document.getElementById("tod");
  toddare.classList.add("remove");

  var toddare = document.getElementById("toddisplay");
  toddare.classList.remove("remove");

  document.getElementById("xmark").textContent = "-15";
  document.getElementById("tmark").textContent = "+15";
  document.getElementById("rmark").textContent = "-10";

  truTH = true;

  var truth1 = [
    "What is the biggest secret you have kept from your parents?",
    "What is the most embarrassing music you listen to?",
    "What is one thing you love most about yourself?",
    "Who is your secret crush?",
    "Who is the last person you creeped on social media?",
    "When was the last time you wet the bed?",
    "If a genie granted you three wishes, what would you ask for and why?",
    "What is your biggest regret?",
    "Where is the weirdest place you have ever gone to the bathroom?",
    "Have you ever ghosted on someone?",
    "What is the boldest pickup line you've ever used?",
    "What celebrity do you think you most look like?",
  ];
  var truth2 = [
    "How many selfies do you take a day?",
    "What is one thing you would stand in line an hour for?",
    "When was the last time you cried?",
    "What is the longest time you've ever gone without showering",
    "What is the most embarrassing top-played song on your phone?",
    "If you had to change your name, what would your new first name be?",
    "What's one silly thing you can not live without?",
    "Who do you text the most?",
    "What is an instant deal breaker in a potential love interest?",
    "If you could only eat one thing for the rest of your life, what would you choose?",
    "What is the biggest lie you ever told your parents?",
    "What's the worst physical pain you've ever experienced?",
  ];
  var truth3 = [
    "If you could only accomplish three things in life, what would they be?",
    "What is the weirdest thing you've ever eaten?",
    "Tell us about the biggest romantic fail you have ever experienced.",
    "What is the strangest dream you've ever had?",
    "What are the top three things you look for in a love interest?",
    "What is your worst habit?",
    "What is your biggest insecurity?",
    "Name one thing you would do if you knew there would be zero consequences.",
    "When is the last time you said you were sorry? For what?",
    "Do you pee in the shower?",
    "Do you still have feelings for any of your exes?",
    "What is the most embarrassing thing you have done to get a crush attention?",
    "Have you ever sent a sextext?",
    "What app do you check first in the morning?",
  ];
  var truth5 = [
    "What is the last movie that made you cry?",
    "What are the five most recent things in your search history?",
    "When is the last time you got caught in a lie",
    "Have you ever had a paranormal experience?",
    "If you could have lunch with a famous person, dead or alive, who would you pick and why?",
    "If you were handed ksh10,000 right now, what would you spend it on?",
    "Have you ever cheated on an exam?",
    "What unexpected part of the body do you find attractive?",
    "79. What is the most awkward thing you have ever been caught doing?",
    "If you had the choice to never have to sleep again, would you take it?",
    "If you had to get a tattoo today, what would it be?",
    "What is the weirdest place you have kissed/hooked up with someone?",
    " What is the longest you have ever gone without brushing your teeth?",
  ];
  var truth6 = [
    "Even if you could be paid $1 million for it, what is something you would never do?",
    "If you could travel to the past and meet one person, who would it be?",
    "What popular TV show or movie do you secretly hate",
    "When have you been in the most trouble in school?",
    "What is the luckiest thing that is ever happened to you?",
    "Do you believe in an afterlife?",
    "Do you believe in soul mates?",
    "What is the weirdest thing you do when you are alone?",
    "What is the most embarrassing nickname you have ever been given?",
    "If you could trade lives with any person you know for a day, who would it be?",
    "What is the worst thing you have ever said to anyone?",
    "What is the scariest dream you have ever had?",
    "What superstitions do you believe in?",
    "What is the weirdest thing you have in your bedroom",
  ];
  var truth7 = [
    "Do you sing in the shower? What was the last song you belted out",
    "Have you ever started a rumor about someone? What was it?",
    " If you could talk to a fortune teller, what would you ask them?",
    "Have you ever given a fake number?",
    "What is more important to you: love or money?",
    "Have you ever fallen in love at first sight?",
    "What is your wildest fantasy?",
    "Have you ever been in a fight?",
    "Have you ever ditched someone before?",
    "What is your favorite possession?",
    "Would you marry someone rich even if you were not in love with them?",
  ];
  var truth8 = [
    "Have you ever lied to a friend?",
    "Have you ever said “I love you” to someone and not meant it?",
    "Is there an ex with whom you could consider reconciling?",
    "What bridges are you glad that you burned?",
    "What do you think happens when you die?",
    "What was your biggest childhood fear?",
    "What was your first heartbreak?",
    "What is the biggest mistake you have ever made?",
    "What is something you are glad your mom does not know about you?",
    "What was the best compliment you have ever received?",
    "What is the scariest thing that is ever happened to you?",
  ];
  var truth9 = [
    "Have you ever fallen out of love?",
    "Name one childish thing that you still do.",
    "Who was your first love?",
    "If you were stranded on an island, who would you want to be stranded with?",
    "What is your dream career?",
    "What makes you the happiest?",
    "What does falling in love feel like to you?",
    "What is the weirdest thing you have ever done in front of a mirror?",
  ];
  var truths = [
    ...truth1,
    ...truth2,
    ...truth3,
    ...truth5,
    ...truth6,
    ...truth7,
    ...truth8,
    ...truth9,
  ];
  let z = [];
  function random() {
    do {
      var index = Math.floor(Math.random() * truths.length);
    } while (z.indexOf(index) > -1);
    z.unshift(index);

    return index;
  }
  function displaytruth(t) {
    document.getElementById("truthordare").textContent = truths[t];
  }

  return displaytruth(random());
}
//*FUNCTION FOR TRUTH SCORE ADDER
function scoretruthskip() {
  if ((currentPlayer = players[playerindex])) {
    playerscores[playerindex] -= 5;
    currentscore = playerscores[playerindex];
  }
  document.getElementById("score").innerHTML = ":" + currentscore;
}

function scoretruthcheck() {
  if ((currentPlayer = players[playerindex])) {
    playerscores[playerindex] += 15;
    currentscore = playerscores[playerindex];
  }
  document.getElementById("score").innerHTML = ":" + currentscore;
}

function scoretruthcancel() {
  if ((currentPlayer = players[playerindex])) {
    playerscores[playerindex] -= 15;
    currentscore = playerscores[playerindex];
  }
  document.getElementById("score").innerHTML = ":" + currentscore;
}

//*GOES BACK TO THE RIDDLE SECTION
function backToRiddle() {
  var toddare = document.getElementById("toddisplay");
  toddare.classList.add("remove");

  var playersection = document.getElementById("playsection");
  playersection.classList.remove("remove");

  var truth = document.getElementById("truth");
  truth.classList.add("disabled");

  var dare = document.getElementById("dare");
  dare.classList.add("disabled");
}

//*FUNCTION TO BE CALLED WHEN A PLAYER CHOOSES THE DARE SECTION IT SET THE trUTH TO FALSE SO AS THE DARE SECTION VALUES WOULD BE USE
function dareDisplay() {
  var toddare = document.getElementById("tod");
  toddare.classList.add("remove");

  var toddare = document.getElementById("toddisplay");
  toddare.classList.remove("remove");

  document.getElementById("xmark").textContent = "-15";
  document.getElementById("tmark").textContent = "+20";
  document.getElementById("rmark").textContent = "-10";

  truTH = false;

  var dare1 = [
    "Read out the last dirty text you sent",
    "Pretend to be a food item of your choice",
    "Show the most embarrassing photo on your phone",
    "Show the last five people you texted and what the messages said",
    "Yell out the first word that comes to your mind",
    "Keep your eyes closed until it's your go again",
    "Try and get all the the toes on one foot in your mouth",
    "Send a sext to the last person in your phonebook",
    "Call the first person in your phonebook and howl like a wolf",
    "Update your relationship status to 'engaged' on Facebook",
    "Perform a seductive dance for the whole group",
    "Sit in the corner of the room without speaking to anyone for the next 10 minutes",
  ];
  var dare2 = [
    "Let the rest of the group DM someone from your Instagram account",
    "Do 100 squats",
    "Say something dirty to the person on your left",
    "Give a foot massage to the person on your right",
    "Yell out the first word that comes to your mind",
    "Give a lap dance to someone of your choice",
    "Repeat everything the person on your right is saying until it's your turn again",
    "Remove four items of clothing",
    "Show your orgasm face",
    "Try not to laugh for the next 10 minutes",
    "For the next 10 minutes, every time someone asks you something, respond with a bark",
    "Add all of your exes into a group chat and asked what went wrong",
    "Do your best impression of a fish out of water",
    "Show off your best dance moves for the full duration of a song.",
  ];
  var dare3 = [
    "Do your best sexy crawl",
    "Pretend to be the person to your right for 10 minutes",
    "Eat a snack without using your hands",
    "Whisper a secret to the person on your left",
    "Say two honest things about everyone else in the group",
    "Twerk for a minute",
    "Try and make the group laugh as quickly as possible",
    "Try to put your whole fist in your mouth",
    "Tell everyone an embarrassing story about yourself",
    "Try to lick your elbow",
    "Say everything in a whisper for the next 10 minutes",
    "Attempt to impersonate everyone in the room",
    "Smile as widely as you can and hold it for two minutes",
    "Attempt the first TikTok dance on your for you page",
    "Speak in a luhya accent until your next turn",
  ];
  var dare4 = [
    "Smell another player's armpit",
    "Do your best celebrity impression",
    "Play air guitar for 2 minutes straight",
    "Howl like a wolf for two minutes",
    "Dance without music for two minutes",
    "Pole dance with an imaginary pole",
    "Let someone else tickle you and try not to laugh",
    "Scroll through your phone book until someone says stop. You either have to call or delete that person.",
    "Put your clothing on backwards for the rest of the evening",
    "Try and make yourself cry in front of the group",
    "Tell the group two truths and a lie, and they have to guess which one the lie is",
    "Give a personalised insult to everyone in the room",
    'Call the first person in your contacts list and sing them "Happy Birthday."',
  ];
  var dare5 = [
    "Let someone order something random on your Amazon account (£10 or under)",
    "Walk around the room like a chicken for one minute",
    "Share with the group your worst vomit story",
    "Go outside and sing 'Football's Coming Home' at full volume",
    "Allow someone else in the group to blindfold you and feed you one item out of the fridge",
    "Read out the last five things on your search history",
    "Let another player draw a tattoo on your foot",
    "Make a rap about your favourite book",
    "Reveal what you think everyone in the group will be up to in five years time",
    "Say yes to everything for the next 1 minute",
    "Say two honest things about everyone else in the group",
    "Try to talk without opening your mouth",
    "ost an embarrassing selfie to your Instagram Story",
  ];
  var dare6 = [
    "Let the other players pose you and remain in that position until your next turn.",
    "Call a random acquaintance and tell them you want to break up",
    "Read the last text your ex sent you",
    "Call a random acquaintance and tell them you want to break up.",
    "Video chat the person of your choice but pick your nose through the entire conversation",
    "Let another player style your hair and leave it that way for the rest of the game",
    "Do a freestyle rap about the other players for one minute.",
    "Make a silly face and keep it that way until someone in the group laughs",
    "Sing instead of speaking any time you talk for three turns",
    "Talk and act like a celebrity until the group can guess who you are (this could go multiple turns!)",
  ];
  var dare7 = [
    "Post an unflattering selfie to your favorite social media account",
    "Do an impression of another player until your next turn.",
    "Switch clothes with another player for the rest of the game.",
    "Give another player your phone and let them send a social media DM to anyone they want.",
    "Show the weirdest item you have in your purse/pockets.",
    "Read the last text message you sent out loud.",
  ];
  var dares = [
    ...dare1,
    ...dare2,
    ...dare3,
    ...dare4,
    ...dare5,
    ...dare6,
    ...dare7,
  ];

  let z = [];
  function random() {
    do {
      var index = Math.floor(Math.random() * dares.length);
    } while (z.indexOf(index) > -1);
    z.unshift(index);

    return index;
  }
  function displaytruth(t) {
    document.getElementById("truthordare").textContent = dares[t];
  }

  return displaytruth(random());
}

//*FUNCTION TO AWARD SCORE IN DARE SECTION
function scoredareskip() {
  if ((currentPlayer = players[playerindex])) {
    playerscores[playerindex] -= 10;
    currentscore = playerscores[playerindex];
  }
  document.getElementById("score").innerHTML = ":" + currentscore;
}

function scoredarecheck() {
  if ((currentPlayer = players[playerindex])) {
    playerscores[playerindex] += 20;
    currentscore = playerscores[playerindex];
  }
  document.getElementById("score").innerHTML = ":" + currentscore;
}

function scoredarecancel() {
  if ((currentPlayer = players[playerindex])) {
    playerscores[playerindex] -= 15;
    currentscore = playerscores[playerindex];
  }
  document.getElementById("score").innerHTML = ":" + currentscore;
}

//*FUNCTION TO AWARD SCORE IN TOD SECTION
function tick() {
  if (truTH) {
    scoretruthcheck();
  } else {
    scoredarecheck();
  }
}

function sskip() {
  if (truTH) {
    scoretruthskip();
    truthDisplay();
  } else {
    scoredareskip();
    dareDisplay();
  }
}

function cAncel() {
  if (truTH) {
    scoretruthcancel();
  } else {
    scoredarecancel();
  }
}

//*TRUTH OR DARE BUTTONS
var dare = document.getElementById("dare");
var truth = document.getElementById("truth");

//* check,cancel and skip button
var check = document.getElementById("tbutton");
var cancelb = document.getElementById("wbutton");
var skip = document.getElementById("sbutton");

/****************
 * !END OF TRUTH OR DARE
 * !END OF TRUTH OR DARE
 * !END OF TRUTH OR DARE
 *************/

/*********************************************
 * !GAMEOVER  SECTIONS
 * !GAMEOVER SECTIONS
 * !GAMEOVER SECTIONS
 *************************************************/
/**
 * *FUNCTION TO CREATE TABLE
 */
var h = 0;
var tableId = ["playerthree", "playerfour", "playerfive", "playersix"];
var tdscores = ["scorethree", "scorefour", "scorefive", "scoresix"];

function hr() {
  return h++;
}

function tables() {
  var tablebody = document.getElementById("tablebody");

  var tablerow = document.createElement("tr");
  tablebody.appendChild(tablerow);

  var tdname = document.createElement("td");
  tdname.scope = "row";
  tdname.id = tableId[h];
  tablerow.appendChild(tdname);

  var tdscore = document.createElement("td");
  tdscore.id = tdscores[h];
  tablerow.appendChild(tdscore);

  hr();
}
function createTablerow() {
  var p = [...document.querySelectorAll(".progress")];
  if (p.length > 5) {
    alerts.classList.remove("remove");
    alertmessage.textContent = "MAXIMUM NUMBER OF PLAYERS REACHED";
  } else {
    tables();
  }
}

function deleteTablerow() {
  numberOfProgressBar();
  if (progressbarss.length > 2) {
    var containerbox = document.getElementById("tablebody");
    containerbox.lastElementChild.remove();
  }
}

/****
 * *FUNCTION IN CHARGE OF THE DISPLAYS IN GAMEOVER
 *
 */
function gameoverDisplay() {
  //*sets the header to tell the player the game is over
  document.querySelector("#gameover").innerHTML = "GAMEOVER";

  //*gameover display
  var gameoverd = document.querySelector(".gameoverContainer");
  gameoverd.classList.remove("remove");

  //*hides riddle play screen
  var riddledisplay = document.getElementById("riddles");
  riddledisplay.classList.add("remove");
  var useranserdisplay = document.querySelector(".useranswer");
  useranserdisplay.classList.add("remove");

  //*creating the winner's name
  var nick = document.getElementById("winnerplayer");
  var highscore = Math.max(...playerscores);
  var winnerindex = playerscores.indexOf(highscore);
  nick.innerHTML = players[winnerindex].toUpperCase() + " " + "WINS";

  //*creating players scoring board:
  if (players.length == 2) {
    document.getElementById("playeroone").innerHTML = players[0].toUpperCase();
    document.getElementById("pllayertwo").innerHTML = players[1].toUpperCase();
  } else if (players.length == 3) {
    document.getElementById("playeroone").innerHTML = players[0].toUpperCase();
    document.getElementById("pllayertwo").innerHTML = players[1].toUpperCase();
    document.getElementById(tableId[0]).innerHTML = players[2].toUpperCase();
  } else if (players.length == 4) {
    document.getElementById("playeroone").innerHTML = players[0].toUpperCase();
    document.getElementById("pllayertwo").innerHTML = players[1].toUpperCase();
    document.getElementById(tableId[0]).innerHTML = players[2].toUpperCase();
    document.getElementById(tableId[1]).innerHTML = players[3].toUpperCase();
  } else if (players.length == 5) {
    document.getElementById("playeroone").innerHTML = players[0].toUpperCase();
    document.getElementById("pllayertwo").innerHTML = players[1].toUpperCase();
    document.getElementById(tableId[0]).innerHTML = players[2].toUpperCase();
    document.getElementById(tableId[1]).innerHTML = players[3].toUpperCase();
    document.getElementById(tableId[2]).innerHTML = players[4].toUpperCase();
  } else {
    document.getElementById("playeroone").innerHTML = players[0].toUpperCase();
    document.getElementById("pllayertwo").innerHTML = players[1].toUpperCase();
    document.getElementById(tableId[0]).innerHTML = players[2].toUpperCase();
    document.getElementById(tableId[1]).innerHTML = players[3].toUpperCase();
    document.getElementById(tableId[2]).innerHTML = players[4].toUpperCase();
    document.getElementById(tableId[3]).innerHTML = players[5].toUpperCase();
  }

  //*Creating score board table
  if (players.length == 2) {
    document.getElementById("scoreone").innerHTML = playerscores[0];
    document.getElementById("scoretwoo").innerHTML = playerscores[1];
  } else if (players.length == 3) {
    document.getElementById("scoreone").innerHTML = playerscores[0];
    document.getElementById("scoretwoo").innerHTML = playerscores[1];
    document.getElementById(tdscores[0]).innerHTML = playerscores[2];
  } else if (players.length == 4) {
    document.getElementById("scoreone").innerHTML = playerscores[0];
    document.getElementById("scoretwoo").innerHTML = playerscores[1];
    document.getElementById(tdscores[0]).innerHTML = playerscores[2];
    document.getElementById(tdscores[1]).innerHTML = playerscores[3];
  } else if (players.length == 5) {
    document.getElementById("scoreone").innerHTML = playerscores[0];
    document.getElementById("scoretwoo").innerHTML = playerscores[1];
    document.getElementById(tdscores[0]).innerHTML = playerscores[2];
    document.getElementById(tdscores[1]).innerHTML = playerscores[3];
    document.getElementById(tdscores[2]).innerHTML = playerscores[4];
  } else {
    document.getElementById("scoreone").innerHTML = playerscores[0];
    document.getElementById("scoretwoo").innerHTML = playerscores[1];
    document.getElementById(tdscores[0]).innerHTML = playerscores[2];
    document.getElementById(tdscores[1]).innerHTML = playerscores[3];
    document.getElementById(tdscores[2]).innerHTML = playerscores[4];
    document.getElementById(tdscores[3]).innerHTML = playerscores[5];
  }
}

//?THIS FUNCTION REMOVES THE SCORE AND NAME ELEMENTS(displays)

function namescore() {
  var playername = document.getElementById("playername");
  var sccore = document.getElementById("score");
  playername.remove();
  sccore.remove();
}

/********************
 ** THIS FUNCTION DELETES THE SUBMIT AND NEXT BUTTON WHEN THE GAME IS OVER SO AS A NEW BUTTON IS CREATED
 *****************/

function remover() {
  submit.remove();
  userInput.remove();
  namescore();
}

/* *********
 *THIS FUNCTION CREATES A BUTTON CALLED BACK TO MENU,THIS BUTTON TAKES PLAYER TO MAIN MENU TO START A NEW GAME AGAIN
 *********/

function backToMenu() {
  var button = document.createElement("button");
  button.textContent = "Back to main menu";
  button.id = "back";
  button.classList.add("btn");
  button.classList.add("btn-sm");
  button.classList.add("btn-md");

  var buttonContainer = document.querySelector(".batttlee");
  buttonContainer.appendChild(button);

  //*on click takes the use to the main menu
  button.onclick = () => {
    window.location.href = "index.html";
  };
}

/**
 * !THIS FUNCTION IS THE GAMEOVER FUNCTION THAT IS IN CHARGE OF ALL THE OTHER GAME OVER FUNCTIONS
 */

function gameover() {
  gameoverDisplay(); //tell the user the game is over and displays the player score
  remover(); //delete submit and next button
  backToMenu(); //create a button to take the user back to the main menu
}

/**
 * !FUNCTION TO CALL THE GAMEOVER
 */

function master(x) {
  if(players.length ==2){
    if (x.length > 16) {
      checkeToD=false;
      gameover();  
    }
  }else if(players.length == 3){
    if (x.length > 24) {
      checkeToD=false;
      gameover();
    }
  }else if(players.length == 4){
    if (x.length > 32) {
      checkeToD=false;
      gameover();
    }
  }else if(players.length == 5){
    if (x.length > 40) {
      checkeToD=false;
      gameover();
    }
  }else{
    if (x.length > 48) {
      checkeToD=false;
      gameover();
    }
  }
}

/**
 * *THIS FUNCTION automatic IS CALLED ONLY WHEN THE KEY DOWN (ENTER )IS PRESSED SO AS TO ENABLE THE PLAYER WHICH CARRY OUT THE FUNCTION OF NEXT BUTTON
 */
//! polices is in charge to avoid conflict between submit button and enter key
//!polices will act as a flag in that when a user presses submit key the enter key will be in active
//!as well for the enter key it will make submit inactive.

let police1 = false;
let police2 = false;

function automatic() {
  displayRiddle(random()); //*displays the riddles
  clearresponse(); //*clear response of the user for next riddle
  delit(); //*delete the displaycorrect for next riddle
  resetcheckonce(); //*reset checkonce
  master(z); //*master to call gameover

  //!we set the police variable false for any preference
  police1 = false;
  police2 = false;
}

window.onload = () => {
  button.onclick = () => {
    checck(); //*ADDS NEW PLAYER
    progressbars(); //*ADDS PROGRESS BAR FOR EACH PLAYER
    createTablerow(); //*ADD TABLE ROW
  };
  buttons.onclick = () => {
    deleteplayer(); //*DELETES A PLAYER
    dleteProgressbarr(); //*DELETES PROGRESS BAR FOR EACH PLAYER
  };

  document.getElementById("batt").onclick = () => {
    checkinput(); //*check if the user has filled all the input space;
    displayRiddle(random()); //*display the current riddle
    numberOfplayers(); //*GETs number of players and player names and put them in an array
    displayPlayers(); //*Displays the current player name and score
  };

  //*when called it set police2 true to avoid enter being called
  submit.onclick = () => {
    if (!police1) {
      onlyonce(); //!It emmbedds the checkit function that is in charge of checking the user input ,the onlyonce enables the checkit function to only be called once
      setTimeout(automatic, 4000);
      police2 = true;
    }
  };

  /**********
   * *TOD BUTTONS
   */
  truth.onclick = () => {
    truthDisplay();
  };
  dare.onclick = () => {
    dareDisplay();
  };

  check.onclick = () => {
    tick();
    setTimeout(backToRiddle, 1000);
    setTimeout(changePlayer, 800); //*changes the current player
  };
  cancelb.onclick = () => {
    cAncel();
    setTimeout(backToRiddle, 1000);
    setTimeout(changePlayer, 800); //*changes the current player
  };
  skip.onclick = () => {
    sskip();
  };

  /****
   * !USING KEY DOWN EVENT TO SUBMIT AND NEXT A RIDDLE
   * ?when called it set police! true to avoid it being called
   */

  document.addEventListener("keydown", (event) => {
    if (event.key == "Enter") {
      if (!police2) {
        onlyonce();
        setTimeout(automatic, 4000);
        police1 = true;
      }
    }
  });
};
