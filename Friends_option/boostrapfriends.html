<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Title</title>
    <!-- Required meta tags -->
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />

    <!-- Bootstrap CSS v5.2.1 -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />
    <link rel="stylesheet" href="boostrapfriends.css" />
   
  </head>

  <body>
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
 
          <!--!START OF REG FORM-->
          <div class="playersform border mx-auto mt-3" id="playformreg">
    
            <div class="addplayer">
                 <div class="btn-group">
              <button type="button" class="btn btn-primary btn-sm btn-md" id="addplayers">
                ADD PLAYER
              </button>
              <button type="button" class="btn btn-danger btn-sm btn-md" id="deleteplayers">
                DELETE PLAYER
              </button>
                </div>
            </div>

            <div class="alert alert-danger alert-dismissible fade show remove"  role="alert" id="alert">
                <button
                    type="button"
                    class="btn-close"
                    data-bs-dismiss="alert"
                    aria-label="Close"
                ></button>
            
                <strong  id="alertmessage"></strong> 
            </div>
            

            <div class="playernames container " id="playernamess">

              <div class="mt-3">
                <input
                  type="text"
                  class="form-control playeers text-info"
                  placeholder="PLAYER ONE"
                />
              </div>

              <div class="mt-3">
                <input
                  type="text"
                  class="form-control playeers text-info"
                  placeholder="PLAYER TWO"
                  
                />
              </div>

            </div>
                  
            <div class="button w-100 d-flex mt-3">
                <button
                    type="button"
                    class="btn btn-primary mx-auto"
                    id="batt"
                >
                    battle
                </button>
                
            </div>
          </div>
          <!--!END OF REG FOR,-->

<!--! START OF HEAD SECTION-->
          <div class="gameplayhead remove" id="headsection">
            <div class="row">
              <div class="col-12">
                <!--!HEAD SECTION-->
                <div class="head remover" id="header">
                  <div class="row"><!--!remover class-->
                   <div class="col-4">
                   <div class="scoreboard d-flex flex-row">
                     <p id="playername" class="text-sm text-md fs-4 mt-2"> </p>
                     <p id="score" class="text-sm text-md fs-4 mt-2">  </p>
                   </div>
                   </div>
             
                   <div class="col-4">
                     <div class="gameover d-sm-flex align-items-center">
                       <p   id="gameover" class="text-sm text-md fs-2 text-center"></p>
                     </div>
                   </div>
             
             
             
                   <div class="col-4 d-flex justify-content-end ">
                     <button
                     type="button"
                     class="btn btn-danger btn-sm btn-md h-100 mt-0 "
                     data-bs-toggle="modal"
                     data-bs-target="#myModal"
                     id="modalbutton"
                      >
                     STATS
                    </button>
                    </div> 
             
                 </div>
                 
               </div>
                 <!--!END OF HEAD SECTION-->
             
                  <!--! The Modal -->
                  <div class="modal" id="myModal">
                   <div class="modal-dialog">
                     <div class="modal-content">
             
                       <!-- ?Modal Header -->
                       <div class="modal-header">
                         <h4 class="modal-title">STATS</h4>
                         <button
                           type="button"
                           class="btn-close"
                           data-bs-dismiss="modal"
                         ></button>
                       </div>
             
                       <!-- ?Modal body -->
                       <div class="modal-body" id="modall">
                           <div class="progress" style="height: 20%;">
             
                               <div
                                 class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar"
                                
                                 id="progress1"
                               >
                               <p class="text-danger fs-4"></p>
                               </div>
                         
                             </div> 
                             <p id="playerone10" class="text-danger "></p>
             
                            
                             <div class="progress mt-2" style="height: 20%;">
                               <div
                                 class="progress-bar progress-bar-striped progress-bar-animated bg-danger"
                                 role="progressbar"
                                 id="progress2"
                               >
                               <p class="text-danger fs-4"></p>
                               </div>
                             </div>
                             <p id="playerone12" class="text-danger"></p>

                             
             
                       </div>
             
                       <!-- ?Modal footer -->
                       <div class="modal-footer">
                         <button
                           type="button"
                           class="btn btn-danger"
                           data-bs-dismiss="modal"
                         >
                           Close
                         </button>
                       </div>
             
                     </div>
                   </div>
                 </div>
             
             </div>
             <!--!END OF MODAL-->
                <!--!END OF  HEAD SECTION-->
              </div>
            </div>
          </div>
<!--!END OF  HEADER SECTION-->

<!--!START OF THE GAME CONTAINER-->
<div class="container riddle remove" id="playsection"><!--!remover class-->
  <div class="row">
    <div class="col-12">
      <div class="riddlecontent">

        <!--!GAME OVER GAME OVER  GAMEOVER DISPLAY-->
        <div class="gameoverContainer remove">

          <div class="imager">
            <img
              src="real_number_1-removebg-preview.png"
              class="img-fluid rounded-top"
              alt="images"
              id="image"
            />
          </div>

          <div class="winner">
            <p id="winnerplayer"></p>
          </div>
           

          <div class="statistics">
            <div
              class="table-responsive"
            >
              <table
                class="table table-primary table-striped table-dark table-hover table-bordered"
              >
                <thead>
                  <tr>
                    <th scope="col">PLAYERS</th>
                    <th scope="col">SCORES</th>
                  </tr>
                </thead>
                <tbody id="tablebody">
                  <tr class="">
                    <td scope="row" id="playeroone"></td>
                    <td id="scoreone"></td>
                    
                  </tr>
                  <tr class="">
                    <td scope="row" id="pllayertwo"></td>
                    <td id="scoretwoo"></td>
                  </tr>

                </tbody>
              </table>
            </div>
          </div>
        </div>
        <!--!END OF GAME OVER CONTAINER-->

 
         <!--!RIDDLE -->
        <div class="containersriddle" id="riddles"><!--!remover class-->
          <p id="riddle" class="text-sm text-md ">
            
          </p>
        </div>


        <div class="useranswer">
          <div class="mb-3">
            <label for="useranswer" class="form-label"></label>
            <input
              type="text"
              name="useranswer"
              id="useranswer"
              class="form-control"
              placeholder="Enter your answer"
            />
          </div>
        </div>


        <div class="displayCorrect">
          <p id="correctdisplay" class="text-sm text-md fs-3 fw-bold "></p>
        </div>


        <div class="batttlee">
          <button
            type="button"
            class="btn btn-primary w-50"
            id="submit"
          >
            submit
          </button>
        </div>
          <!--!END OF RIDDLE-->

      </div>
    </div>
  </div>
</div>
<!--!END OF THE GAME CONTAINER-->

<!--!START OF TRUTH OR DARE DISPLAY BUTTON-->
<div class="container border remove" id="tod">
  <div class="mess">
    <p class="text-center fs-3 fw-bold text-danger">Choose wisely</p>
  </div>
 
  <div class="row border" id="truthdare">
    <div class="col-6  h-100">
      <button
        type="button"
        class="btn btn-outline-primary h-100 w-100 btn-sm fs-1 fw-bold disabled"
        id="truth"
      >
        TRUTH
      </button> 
      </div>
      <div class="col-6">
        <button
          type="button"
          class="btn btn-outline-danger h-100 w-100 btn-sm fs-1 fw-bold  disabled "
          id="dare"
        >
         DARE    
        </button>
    </div>
  </div>
</div>
<!--!END OF TRUTH OR DARE DISPLAY BUTTON -->

<!--!START OF DARE OR TRUTH-->
<div class="container remove " id="toddisplay">
  <div class="row h-100">
    <div class="col-12 h-100">
      <div class="tod mt-3">
        <p id="truthordare" class="text-center">
          
        </p>

      </div>
      <div class="butttons d-flex flew-row justify-content-around">
        <div class="wrong">
          <button
            type="button"
            class="btn btn-danger btn-sm"
            id="wbutton"
          >
          <i class="fa-solid fa-xmark"></i>
          </button>
          <P class="text-primary text-danger text-center" id="xmark"></P>  
        </div>

        <div class="tick">
          <button
            type="button"
            class="btn btn-primary"
            id="tbutton"
          >
          <i class="fa-solid fa-check"></i>
          </button>
           <P class="text-primary text-center" id="tmark"></P>  
        </div>

        <div class="skip">
          <button
            type="button"
            class="btn btn-info"
            id="sbutton"
          >
          <i class="fa-solid fa-arrow-rotate-right"></i>
          </button>
          <P class="text-primary text-center" id="rmark"></P>
        </div>

      </div>

    </div>
  </div>
</div>
<!--!END OF DARE OOR TRUTH-->













        </div>
      </div>
    </div>


    <script src="boostrapfriends.js"></script>
    <script src="https://kit.fontawesome.com/998b8511eb.js" crossorigin="anonymous"></script>
    <script
      src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
      integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r"
      crossorigin="anonymous"
    ></script>

    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.min.js"
      integrity="sha384-BBtl+eGJRgqQAUMxJ7pMwbEyER4l1g+O15P+16Ep7Q9Q+zqX6gSbd85u4mG4QzX+"
      crossorigin="anonymous"
    ></script>
  </body>
</html>
