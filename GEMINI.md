##  🎮 Project Overview

This project is a Riddle / Truth or Dare Game designed to provide an engaging user experience through animations, interactive gameplay, and smooth navigation. The front-end will be developed using React + Vite, and all components will be placed in a new folder called frontEnd.
## 🔁 Project Flow
1. Landing Page

    Purpose: Introduce the game and engage the user visually.

    Action: Convert the existing ./screen_template/riddle_landingpage.html to a React component OR design a custom landing page.

    Requirements:

        Clearly explain the game concept (riddle/truth or dare).

        Include beautiful animations to create an appealing first impression.

        On interaction (e.g., "Get Started" or "Enter" button), navigate to the Authentication Page.

2. Authentication Page

You may:

    Adapt the template from ./screen_template/authentication.html, OR

    Create a fully custom authentication flow with the following requirements:

Sign In Requirements

    User must provide:

        Email or Username

        Password

    Features:

        Show/Hide Password Toggle

        Error Message Box for incorrect credentials

        Option to sign in using Google Email (dummy integration)

        Include smooth animations for transitions and feedback

Sign Up Requirements

    User must provide:

        Email

        Username

        Password

        Profile photo (choose from:

            Gallery upload

            Pre-made avatar icons)

    Features:

        Show/Hide Password Toggle

        Error Message Box for validation feedback

        Option to sign up using Google Email (dummy integration)

        Include smooth animations

    After successful registration:

        Redirect the user to a Verification Code Page

3. Verification Code Page

    Purpose: Simulate email verification

    Requirements:

        Prompt the user to enter a dummy verification code

        Upon valid input, proceed to the Dashboard

4. User Dashboard

    Purpose: Serve as the central hub for the user after login/signup

    Options:

        Convert ./screen_template/dashboard_test.html into a React component, OR

        Create a custom dashboard with improved UI/UX

    Should display:

        User info (e.g., profile icon, username)

        Navigation to game modes and other features

📁 Frontend Setup

    Create a new directory named frontEnd

    Initialize a React + Vite project within this folder

    Implement all components and pages described above within this project