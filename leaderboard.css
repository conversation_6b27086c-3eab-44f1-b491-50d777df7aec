/* Leaderboard Styling */
.leaderboard-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 30px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 15px;
    box-shadow: 0 0 30px rgba(139, 0, 0, 0.5);
}

.leaderboard-filters {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    gap: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.filter-group label {
    color: white;
    margin-bottom: 5px;
}

.form-select {
    background-color: rgba(51, 0, 0, 0.9);
    color: white;
    border: 1px solid var(--blood-red);
}

.leaderboard-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 20px;
}

.leaderboard-table thead {
    background-color: rgba(139, 0, 0, 0.3);
}

.leaderboard-table th, 
.leaderboard-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid rgba(139, 0, 0, 0.2);
    color: white;
    word-break: break-word;
}

.leaderboard-table tr:nth-child(even) {
    background-color: rgba(51, 0, 0, 0.3);
}

.leaderboard-table tr:hover {
    background-color: rgba(139, 0, 0, 0.2);
}

.leaderboard-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.leaderboard-pagination button {
    width: auto;
    padding: 10px 20px;
}

#current-page {
    color: white;
    font-weight: bold;
}

/* User Position Search Styles */
.user-position-search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
    width: 100%;
}

.user-position-search input {
    flex-grow: 1;
    background-color: rgba(51, 0, 0, 0.9);
    color: white;
    border: 1px solid var(--blood-red);
}

.user-position-search button {
    width: auto;
    padding: 10px 20px;
}

.user-position-result {
    width: 100%;
    margin-bottom: 20px;
    text-align: center;
}

.user-position-card {
    background: rgba(51, 0, 0, 0.7);
    border-radius: 10px;
    padding: 20px;
    color: white;
    max-width: 400px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(139, 0, 0, 0.3);
}

.user-position-card h3 {
    font-family: 'Nosifer', cursive;
    color: var(--blood-red);
    margin-bottom: 15px;
}

.user-position-card p {
    margin: 10px 0;
    text-align: left;
}

/* Rank Coloring */
.rank-1 { color: gold; }
.rank-2 { color: silver; }
.rank-3 { color: #CD7F32; }

/* Comprehensive Mobile Responsiveness */
@media screen and (max-width: 768px) {
    .leaderboard-container {
        width: 95vw;
        padding: 15px;
        margin: 0 auto;
    }

    .leaderboard-filters {
        flex-direction: column;
        gap: 10px;
    }

    .filter-group {
        width: 100%;
    }

    .leaderboard-table {
        font-size: 12px;
    }

    .leaderboard-table th, 
    .leaderboard-table td {
        padding: 8px;
    }

    /* User Position Search Mobile Styles */
    .user-position-search {
        flex-direction: column;
        gap: 10px;
    }

    .user-position-search input,
    .user-position-search button {
        width: 100%;
        max-width: none;
    }

    .user-position-card {
        width: 90%;
        padding: 15px;
    }

    /* Pagination Mobile Adjustments */
    .leaderboard-pagination {
        flex-direction: column;
        gap: 10px;
    }

    .leaderboard-pagination button {
        width: 100%;
        max-width: 250px;
    }
}

/* Extreme Mobile Responsiveness */
@media screen and (max-width: 480px) {
    .leaderboard-table {
        font-size: 10px;
    }

    .leaderboard-table th, 
    .leaderboard-table td {
        padding: 6px;
    }

    .user-position-card {
        width: 95%;
        padding: 10px;
        font-size: 14px;
    }
}

/* Accessibility and Touch Improvements */
@media screen and (max-width: 768px) {
    * {
        touch-action: manipulation;
    }

    .leaderboard-table,
    .user-position-search,
    .leaderboard-pagination,
    .btn {
        min-touch-target-size: 44px;
        -webkit-tap-highlight-color: transparent;
    }
}