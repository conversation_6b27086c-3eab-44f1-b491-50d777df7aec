import React, { useState, useRef } from "react";
import './authentication.css'


export function Authentication() {

    const [activetab, switchTab] = useState("signin")



    return (
        <>
            <div className="bg-decoration">
                <div className="floating-shape shape1"></div>
                <div className="floating-shape shape2"></div>
                <div className="floating-shape shape3"></div>
            </div>
            <div className="container">
                <div className="logo">
                    <h1>🎭 Riddle Quest</h1>
                    <p>Truth, Dare & Mystery Await</p>
                </div>
                <div className="tab-buttons">
                    <button className={activetab === "signin" ? "tab-btn active" : "tab-btn"} onClick={() => switchTab("signin")}>Sign In</button>
                    <button className={activetab === "signup" ? "tab-btn active" : "tab-btn"} onClick={() => switchTab("signup")}>Sign Up</button>
                </div>

                 <SignIn classes={activetab} /> 
                   
                <SignUp classes={activetab} /> 

                   
            </div>
        </>
    )

}
function Tesdt(prop) {
    return (
        <>
        { prop.classes === "signup" ? <h1>hello word</h1> : <h2>hello kenya</h2>}
        </>
    )
}



function SignIn(prop) {

    const [visiblePassword, setPasswordVisible] = useState(false);
    const [fieldInputs, setInput] = useState({})
    const [EmailFieldError, setEmailFieldError] = useState({ Error: false, errorMsg: "" })
    const [passwordFieldError, setPasswordFieldError] = useState({ Error: false, errorMsg: "" })
    const [shakeclass, setShakeclass] = useState(false)
    const haserror = false;

    function triggershakeclass() {
        setShakeclass(true)
        setTimeout(() => {
            setShakeclass(false)
        }, 5000);

    }

    function signInWithGoogle() {
        // Simulate Google Sign In
        alert('🚀 Google Sign In would be implemented here!\n\nIn a real app, this would use Google OAuth API.');
    }

    function handleuserinput(e) {
        const name = e.target.name
        const value = e.target.value
        setInput(values => ({ ...values, [name]: value }))
    }

    

    const handleSignIn = async (event) => {
        event.preventDefault();

        // Basic validation
        if (!fieldInputs.email) {
            setEmailFieldError({ Error: true, errorMsg: "Email is required" })
            triggershakeclass()
            return;
        }
        if (!fieldInputs.password) {
            setPasswordFieldError({ Error: true, errorMsg: "Password is required" })
            triggershakeclass()
            return
        }

        if (haserror) {
            return
        }

        try {
            // Send login request to backend
            const response = await fetch('http://localhost:3001/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: fieldInputs.email,
                    password: fieldInputs.password
                })
            });

            const data = await response.json();

            if (data.success) {
                alert(`🎉 Welcome back, ${data.user.username}!\n\nRedirecting to the game...`);

                // Clear form
                setInput({});
                setEmailFieldError({ Error: false, errorMsg: "" });
                setPasswordFieldError({ Error: false, errorMsg: "" });

                // Here you could redirect to the game or store user data
                console.log('User logged in:', data.user);

            } else {
                // Handle login errors
                if (data.message.includes('email') || data.message.includes('password')) {
                    setEmailFieldError({ Error: true, errorMsg: data.message });
                    setPasswordFieldError({ Error: true, errorMsg: data.message });
                } else {
                    alert(`❌ Login failed: ${data.message}`);
                }
                triggershakeclass();
            }

        } catch (error) {
            console.error('Login error:', error);
            alert('❌ Network error. Please check if the server is running and try again.');
        }
    }


    return (
        <div className={prop.classes === "signin" ? "form-container active" : "form-container"} id="signin-form">
            <button className="google-btn" onClick={signInWithGoogle}>
                <span>📧</span>
                Continue with Google
            </button>

            <div className="divider">
                <span>or</span>
            </div>

            <form onSubmit={handleSignIn}>
                <div className={shakeclass && EmailFieldError.Error ? "form-group shake" : "form-group"}>
                    <label htmlFor="signin-email">Email Address</label>
                    <input type="email" className={EmailFieldError.Error ? "input-error" : ""} name="email" value={fieldInputs.email || ""} id="signin-email" onChange={handleuserinput} onInput={()=> setEmailFieldError({Error:false,errorMsg:""}) } />
                    <div className="error-message" style={{ display: EmailFieldError.Error ? "block" : "none" }} id="signup-email-error">{EmailFieldError.errorMsg}</div>
                </div>

                <div className={shakeclass && passwordFieldError ? "form-group shake" : "form-group"}>
                    <label htmlFor="signin-password">Password</label>
                    <div className="password-container">
                        <input type={visiblePassword ? "text" : "password"} className={passwordFieldError.Error ? "input-error" : ""} id="signin-password" name="password" value={fieldInputs.password || ""} onChange={handleuserinput} onInput={()=>setPasswordFieldError({Error:false,errorMsg:""})} />
                        <button type="button" className="password-toggle" onClick={() => visiblePassword ? setPasswordVisible(false) : setPasswordVisible(true)}>
                            {visiblePassword ?"🙈": "👁️" }
                        </button>
                    </div>
                    <div className="error-message" style={{ display: passwordFieldError.Error ? "block" : "none" }} id="signin-password-error">{passwordFieldError.errorMsg}</div>
                </div>

                <button type="submit" className="submit-btn">Enter the Game</button>
            </form>
        </div>
    )

}

function SignUp(props) {
    const signUpWithGoogle = () => {
        alert('🚀 Google Sign Up would be implemented here!\n\nIn a real app, this would use Google OAuth API.');
    }

    const fileinputRef = useRef(null)
    const triggerFileInput = () => {
        fileinputRef.current.click()
    }

    const [showPresetAvatars, togglePresetAvatars] = useState(false);//show list of preset avatars
    const [selectedPresetAvatar, setSelectAvatar] = useState(0);// show a selected avatar
    const [avatarPreview, setAvatarPreview] = useState(null)//upload a selected avatar
    const [UploadedImage, setuploadImage] = useState(null)//upload an image from the file
    const [avatarprofile,setavatar]=useState("default image")


    function selectPresetAvatar(element, emoji) {
        setSelectAvatar(element)
        setAvatarPreview(emoji)
         setavatar(emoji)
    }



      //show password visilbility
    const [visiblePassword, setPasswordVisible] = useState(false);
    const [visiblePasswordForConfirm, setConfirmPasswordVisible] = useState(false);



    function handleImageUpload(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function (e) {
                setAvatarPreview(null)
                setuploadImage(e.target.result)
                setavatar(UploadedImage)
                setSelectAvatar(0)
            };
            reader.readAsDataURL(file);
        }
    }


     //collect user input
    const [UserInputs, setUserInput] = useState({})
    function handleuserinput(e) {
        const name = e.target.name
        const value = e.target.value
        setUserInput(values => ({ ...values, [name]: value }))
    }

// submit button animation
const [createUserAccount,setCreatUserAccount]=useState(false)

    //errorstate
    const [shakeclass, setShakeclass] = useState(false)
    const [usernameError,setusernameError]=useState({error:false,Errormsg:""})
    const [userEmailError,setUserEmailError]=useState({error:false,Errormsg:""})
    const [passwordError,setPasswordError]=useState({error:false,Errormsg:""})
    const [confirmPasswordError,setConfirmPasswordError]=useState({error:false,errorMsg:""})

   // const hasErrors = false;

    function triggershakeclass() {
        setShakeclass(true)
        setTimeout(() => {
            setShakeclass(false)
        }, 5000);

    }
    const [passwordStrengthError, setPasswordStrengthError] = useState({});

    function checkPasswordStrength(e) {
        setPasswordError({error:false,Errormsg:""})

        const password = e.target.value;

        if (password.length === 0) {
            setPasswordStrengthError({
                passwordMsg: "",
                passwordStrengthClass: "password-strength",
                passwordStrengthStyle: "none"
            });
            return;
        }

        let strength = 0;
        let feedback = [];

        if (password.length >= 6) strength++;
        else feedback.push('at least 6 characters');

        if (/[A-Z]/.test(password)) strength++;
        else feedback.push('one uppercase letter');

        if (/[a-z]/.test(password)) strength++;
        else feedback.push('one lowercase letter');

        if (/\d/.test(password)) strength++;
        else feedback.push('one number');

        if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
        else feedback.push('one special character');

        let strengthText = '';
        let strengthClass = '';

        if (strength < 2) {
            strengthText = `Weak password. Add: ${feedback.slice(0, 3).join(', ')}`;
            strengthClass = 'strength-weak';
        } else if (strength < 4) {
            strengthText = `Medium strength. Consider adding: ${feedback.slice(0, 2).join(', ')}`;
            strengthClass = 'strength-medium';
        } else {
            strengthText = 'Strong password! 💪';
            strengthClass = 'strength-strong';
        }

        setPasswordStrengthError({
            passwordMsg: strengthText,
            passwordStrengthClass: `password-strength ${strengthClass}`,
            passwordStrengthStyle: "block"
        });

        return strength >= 4;
    }


    function checkPasswordMatch() {
        const password = UserInputs.password
        const confirmPassword = UserInputs.confirmPassword;

        if (confirmPassword.length > 0) {
            if (password !== confirmPassword) {
                setConfirmPasswordError({error:true,errorMsg:"Passwords do not match"})
                return false;
            } else {
                setConfirmPasswordError({error:false,errorMsg:""})
                return true;
            }
        }
    }


    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    async function handleSignUp(event) {
        event.preventDefault();

        let hasErrors = false;

        // Username validation
        if (!UserInputs.username) {
            setusernameError({error:true,Errormsg:"Username is required"})
            triggershakeclass()
            hasErrors = true;
        } else if (UserInputs.username.length < 3) {
            setusernameError({error:true,Errormsg:"Username must be at least 3 characters long"})
            triggershakeclass()
            hasErrors = true;
        }

        // Email validation
        if (!UserInputs.email) {
            setUserEmailError({error:true,Errormsg:"Email is required"})
            triggershakeclass()
            hasErrors = true;
        } else if (!isValidEmail(UserInputs.email)) {
            setUserEmailError({error:true,Errormsg:"Please enter a valid email address"})
            triggershakeclass()
            hasErrors = true;
        }

        // Password validation
        if (!UserInputs.password) {
            setPasswordError({error:true,Errormsg:"Password is required"})
            triggershakeclass()
            hasErrors = true;
        } else if (UserInputs.password.length < 6) {
            setPasswordError({error:true,Errormsg:"Password must be at least 6 characters long"})
            triggershakeclass()
            hasErrors = true;
        } else if (!checkPasswordStrength()) {
            setPasswordError({error:true,Errormsg:"Please create a stronger password"})
            triggershakeclass()
            hasErrors = true;
        }

        // Confirm password validation
        if (!UserInputs.confirmPassword) {
            setConfirmPasswordError({error:true,errorMsg:"Please confirm your password"})
            triggershakeclass()
            hasErrors = true;
        } else if (UserInputs.confirmPassword !== UserInputs.password) {
            setConfirmPasswordError({error:true,errorMsg:"Passwords do not match"})
            triggershakeclass()
            hasErrors = true;
        }

        if (hasErrors) {
            return;
        }

        // Show loading state
        setCreatUserAccount(true)

        try {
            // Send data to backend API
            const response = await fetch('http://localhost:3001/api/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: UserInputs.username,
                    email: UserInputs.email,
                    password: UserInputs.password,
                    avatar: avatarprofile
                })
            });

            const data = await response.json();

            if (data.success) {
                setCreatUserAccount(false)
                alert(`🎉 Welcome to Riddle Quest, ${UserInputs.username}!\n\nYour account has been created successfully!`);

                // Clear form
                setUserInput({});
                setAvatarPreview(null);
                setuploadImage(null);
                setavatar("default image");
                setSelectAvatar(0);

                // Clear any errors
                setusernameError({error:false,Errormsg:""});
                setUserEmailError({error:false,Errormsg:""});
                setPasswordError({error:false,Errormsg:""});
                setConfirmPasswordError({error:false,errorMsg:""});

            } else {
                setCreatUserAccount(false)

                // Handle specific errors from backend
                if (data.message.includes('email')) {
                    setUserEmailError({error:true,Errormsg:data.message})
                } else if (data.message.includes('username')) {
                    setusernameError({error:true,Errormsg:data.message})
                } else if (data.message.includes('password')) {
                    setPasswordError({error:true,Errormsg:data.message})
                } else {
                    alert(`❌ Registration failed: ${data.message}`);
                }
                triggershakeclass()
            }

        } catch (error) {
            setCreatUserAccount(false)
            console.error('Registration error:', error);
            alert('❌ Network error. Please check if the server is running and try again.');
        }
    }
    return (
        <div className={props.classes === "signup" ? "form-container active" : "form-container"} id="signup-form">
            <button className="google-btn" onClick={signUpWithGoogle}>
                <span>📧</span>
                Sign up with Google
            </button>

            <div className="divider">
                <span>or</span>
            </div>

            <form onSubmit={handleSignUp}>
                <div className="avatar-section">
                    <div className="avatar-preview" id="avatar-preview">
                        {avatarPreview ? (
                            <span style={{ fontSize: "60px" }}>{avatarPreview}</span>
                        ) : UploadedImage ? (
                            <img src={UploadedImage} alt="Avatar" />
                        ) : (
                            <span className="avatar-placeholder">👤</span>
                        )}

                    </div>

                    <div className="avatar-options">
                        <button type="button" className="avatar-btn" onClick={triggerFileInput}>
                            Upload Photo
                        </button>
                        <button type="button" className="avatar-btn" onClick={()=> showPresetAvatars ? togglePresetAvatars(false) : togglePresetAvatars(true)}>
                            Choose Avatar
                        </button>
                    </div>

                    <input type="file" ref={fileinputRef} id="file-input" accept="image/*" name="image" onChange={handleImageUpload} />

                    <div className="preset-avatars" id="preset-avatars" style={{ display: showPresetAvatars ? "flex" : "none" }}>
                        <div className={selectedPresetAvatar == 1 ? "preset-avatar selected" : "preset-avatar"} style={{ backgroundImage: `url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiNGRjZCNkIiLz4KPGV5ZXMgZmlsbD0iIzMzMzMzMyI+CjxjaXJjbGUgY3g9IjE4IiBjeT0iMjAiIHI9IjMiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyMCIgcj0iMyIvPgo8L2V5ZXM+CjxwYXRoIGQ9Ik0xNSAzMFEyNSAzNSAzNSAzMCIgc3Ryb2tlPSIjMzMzMzMzIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPC9zdmc+')` }} onClick={() => selectPresetAvatar(1, '🙂')}></div>
                        <div className={selectedPresetAvatar == 2 ? "preset-avatar selected" : "preset-avatar"} style={{ backgroundImage: `url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiM2QkM5RkYiLz4KPGV5ZXMgZmlsbD0iIzMzMzMzMyI+CjxjaXJjbGUgY3g9IjE4IiBjeT0iMjAiIHI9IjMiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyMCIgcj0iMyIvPgo8L2V5ZXM+CjxwYXRoIGQ9Ik0xNSAzMFEyNSAzNSAzNSAzMCIgc3Ryb2tlPSIjMzMzMzMzIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPC9zdmc+')` }} onClick={() => selectPresetAvatar(2, '😊')}></div>
                        <div className={selectedPresetAvatar == 3 ? "preset-avatar selected" : "preset-avatar"} style={{ backgroundImage: `url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiNGRkI4NkMiLz4KPGV5ZXMgZmlsbD0iIzMzMzMzMyI+CjxjaXJjbGUgY3g9IjE4IiBjeT0iMjAiIHI9IjMiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyMCIgcj0iMyIvPgo8L2V5ZXM+CjxwYXRoIGQ9Ik0xNSAzMFEyNSAzNSAzNSAzMCIgc3Ryb2tlPSIjMzMzMzMzIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPC9zdmc+')` }} onClick={() => selectPresetAvatar(3, '🤔')}></div>
                        <div className={selectedPresetAvatar == 4 ? "preset-avatar selected" : "preset-avatar"} style={{ backgroundImage: `url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiNBNzg2RkYiLz4KPGV5ZXMgZmlsbD0iIzMzMzMzMyI+CjxjaXJjbGUgY3g9IjE4IiBjeT0iMjAiIHI9IjMiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyMCIgcj0iMyIvPgo8L2V5ZXM+CjxwYXRoIGQ9Ik0xNSAzMFEyNSAzNSAzNSAzMCIgc3Ryb2tlPSIjMzMzMzMzIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPC9zdmc+')` }} onClick={() => selectPresetAvatar(4, '😎')}></div>
                    </div>
                </div>

                <div className= {usernameError.error && shakeclass ? "form-group shake" : "form-group"}>
                    <label htmlFor="signup-username">Username</label>
                    <input type="text" id="signup-username"   className={usernameError.error ? "input-error":""} name="username" value={UserInputs.username || ""} onChange={handleuserinput} onInput={()=> setusernameError({error:false,errorMsg:""})} />
                    <div className="error-message" style={{display:usernameError.error ? "block":"none"}} id="signup-username-error">{usernameError.Errormsg}</div>
                </div>

                <div className= {userEmailError.error && shakeclass ? "form-group shake" : "form-group"} >
                    <label htmlFor="signup-email">Email Address</label>
                    <input type="email" id="signup-email" className={userEmailError.error ? "input-error":""}  name="email" value={UserInputs.email || ""} onChange={handleuserinput} onInput={()=>setUserEmailError({error:false,errorMsg:""})} />
                    <div className="error-message" style={{display:userEmailError.error ? "block":"none"}} id="signup-email-error">{userEmailError.Errormsg}</div>
                </div>

                <div className= {passwordError.error && shakeclass ? "form-group shake" : "form-group"}>
                    <label htmlFor="signup-password">Password</label>
                    <div className="password-container">
                        <input type={visiblePassword ? "text" : "password"} className={passwordError.error ? "input-error":""}   id="signup-password" onInput={checkPasswordStrength} onChange={handleuserinput} name="password" value={UserInputs.password || ""}/>
                        <button type="button" className="password-toggle" onClick={() => visiblePassword ? setPasswordVisible(false) : setPasswordVisible(true)}>
                        {visiblePassword ? "🙈": "👁️" }
                        </button>
                    </div>
                    <div className={passwordStrengthError.passwordStrengthClass} style={{display:passwordStrengthError.passwordStrengthStyle}} id="password-strength">{passwordStrengthError.passwordMsg}</div>
                    <div className="error-message" style={{display:passwordError.error ? "block":"none"}} id="signup-password-error">{passwordError.Errormsg}</div>
                </div>

                <div className= {confirmPasswordError.error && shakeclass ? "form-group shake" : "form-group"}>
                    <label htmlFor="confirm-password">Confirm Password</label>
                    <div className="password-container">
                        <input type={visiblePasswordForConfirm ? "text" : "password"} id="confirm-password" className={confirmPasswordError.error ? "input-error":""} onInput={checkPasswordMatch} name="confirmPassword" onChange={handleuserinput} value={UserInputs.confirmPassword || ""} />
                        <button type="button" className="password-toggle" onClick={() => visiblePasswordForConfirm ? setConfirmPasswordVisible(false) : setConfirmPasswordVisible(true)}>
                            {visiblePasswordForConfirm ? "🙈": "👁️"}
                        </button>
                    </div>
                    <div className="error-message" style={{display:confirmPasswordError.error ? "block":"none"}} id="confirm-password-error">{confirmPasswordError.errorMsg}</div>
                </div>

                <button type="submit" className= {createUserAccount ? "submit-btn loading":"submit-btn" }> {createUserAccount ? "Creating Account..." : "Join the Adventure"}</button>
            </form>
        </div>
    )

}