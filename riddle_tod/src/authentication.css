* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow-x: hidden;
}

/* Animated background elements */
.bg-decoration {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

.floating-shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.shape1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape2 {
    width: 60px;
    height: 60px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

.container {
    background: rgba(20, 20, 35, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.5);
    width: 90%;
    max-width: 450px;
    position: relative;
    z-index: 1;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    text-align: center;
    margin-bottom: 30px;
}

.logo h1 {
    color: #64ffda;
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 0 0 20px rgba(100, 255, 218, 0.3);
    margin-bottom: 10px;
}

.logo p {
    color: #b0bec5;
    font-size: 1rem;
    font-style: italic;
}

.tab-buttons {
    display: flex;
    margin-bottom: 30px;
    background: #2a2a3e;
    border-radius: 12px;
    padding: 5px;
}

.tab-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    color: #90a4ae;
}

.tab-btn.active {
    background: linear-gradient(135deg, #64ffda, #00bcd4);
    color: #1a1a2e;
    box-shadow: 0 4px 12px rgba(100, 255, 218, 0.3);
}

.form-container {
    display: none;
}

.form-container.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #eceff1;
    font-weight: 600;
}

.form-group input {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #455a64;
    border-radius: 12px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #263238;
    color: #eceff1;
}

.form-group input:focus {
    outline: none;
    border-color: #64ffda;
    box-shadow: 0 0 0 3px rgba(100, 255, 218, 0.2);
    background: #37474f;
}

.form-group input::placeholder {
    color: #78909c;
}

.password-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #90a4ae;
    font-size: 18px;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #64ffda;
}

.avatar-section {
    text-align: center;
    margin-bottom: 20px;
}

.avatar-preview {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin: 0 auto 15px;
    background: #2a2a3e;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #455a64;
    overflow: hidden;
    position: relative;
}

.avatar-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    color: #90a4ae;
    font-size: 40px;
}

.avatar-options {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-bottom: 15px;
}

.avatar-btn {
    padding: 8px 16px;
    border: 2px solid #64ffda;
    background: transparent;
    color: #64ffda;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.avatar-btn:hover {
    background: #64ffda;
    color: #1a1a2e;
    box-shadow: 0 0 15px rgba(100, 255, 218, 0.3);
}

.preset-avatars {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 10px;
}

.preset-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.3s ease;
    background-size: cover;
    background-position: center;
}

.preset-avatar:hover {
    border-color: #64ffda;
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(100, 255, 218, 0.4);
}

.preset-avatar.selected {
    border-color: #64ffda;
    box-shadow: 0 0 0 2px rgba(100, 255, 218, 0.5);
}

.google-btn {
    width: 100%;
    padding: 14px;
    border: 2px solid #ea4335;
    background: transparent;
    color: #ea4335;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.google-btn:hover {
    background: #ea4335;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(234, 67, 53, 0.4);
}

.divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
    color: #78909c;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #455a64;
}

.divider span {
    background: rgba(20, 20, 35, 0.95);
    padding: 0 15px;
}

.submit-btn {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #64ffda, #00bcd4);
    color: #1a1a2e;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(100, 255, 218, 0.3);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(100, 255, 218, 0.5);
}

.submit-btn:active {
    transform: translateY(0);
}

#file-input {
    display: none;
}

.error-message {
    color: #ff5252;
    font-size: 14px;
    margin-top: 5px;
    display: none;
    padding: 8px 12px;
    background: rgba(255, 82, 82, 0.1);
    border: 1px solid rgba(255, 82, 82, 0.3);
    border-radius: 8px;
    animation: slideDown 0.3s ease;
}

.success-message {
    color: #4caf50;
    font-size: 14px;
    margin-top: 5px;
    display: none;
    padding: 8px 12px;
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
    border-radius: 8px;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

.input-error {
    border-color: #ff5252 !important;
    box-shadow: 0 0 0 3px rgba(255, 82, 82, 0.2) !important;
}

.password-strength {
    margin-top: 8px;
    padding: 8px;
    border-radius: 6px;
    font-size: 12px;
    display: none;
}

.strength-weak {
    background: rgba(255, 82, 82, 0.1);
    color: #ff5252;
    border: 1px solid rgba(255, 82, 82, 0.3);
}

.strength-medium {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.strength-strong {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border: 2px solid #64ffda;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@media (max-width: 480px) {
    .container {
        padding: 30px 20px;
        margin: 20px;
    }
    
    .logo h1 {
        font-size: 2rem;
    }
    
    .preset-avatars {
        gap: 8px;
    }
    
    .preset-avatar {
        width: 40px;
        height: 40px;
    }
}