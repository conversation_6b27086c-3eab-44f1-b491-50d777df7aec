<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wordplay Riddles - Choose Your Mode</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            color: #fff;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated word background */
        .word-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.08;
        }

        .floating-letter {
            position: absolute;
            font-size: clamp(1.5rem, 4vw, 3rem);
            font-weight: bold;
            color: #ff6b6b;
            animation: letterFloat 20s infinite linear;
            text-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
        }

        .floating-letter:nth-child(even) {
            color: #4ecdc4;
            animation-direction: reverse;
            text-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
        }

        .floating-letter:nth-child(3n) {
            color: #45b7d1;
            animation-duration: 25s;
            text-shadow: 0 0 10px rgba(69, 183, 209, 0.3);
        }

        .floating-letter:nth-child(4n) {
            color: #ffa726;
            animation-duration: 15s;
            text-shadow: 0 0 10px rgba(255, 167, 38, 0.3);
        }

        .floating-word {
            position: absolute;
            font-size: clamp(0.8rem, 2vw, 1.2rem);
            color: rgba(255, 255, 255, 0.1);
            animation: wordDrift 30s infinite linear;
            font-style: italic;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            justify-content: center;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: slideInTop 1s ease-out;
        }

        .back-button {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            animation: fadeIn 1.5s ease-out;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
        }

        .title {
            font-size: clamp(2.5rem, 8vw, 4rem);
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #ffa726);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 4s ease-in-out infinite;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            font-family: 'Georgia', serif;
        }

        .title-icon {
            font-size: clamp(2rem, 6vw, 3rem);
            animation: iconBounce 3s ease-in-out infinite;
        }

        .subtitle {
            font-size: clamp(1.2rem, 4vw, 1.8rem);
            color: #b0b0b0;
            margin-bottom: 15px;
            animation: fadeIn 1.5s ease-out;
            font-style: italic;
        }

        .description {
            font-size: clamp(1rem, 2.5vw, 1.2rem);
            color: #888;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
            animation: fadeIn 2s ease-out;
        }

        .modes-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
            animation: slideUp 1s ease-out 0.8s both;
        }

        .mode-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 40px 30px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
            transform: translateY(30px);
            opacity: 0;
            animation: cardReveal 0.8s ease-out forwards;
        }

        .mode-card:nth-child(1) { animation-delay: 0.3s; }
        .mode-card:nth-child(2) { animation-delay: 0.5s; }
        .mode-card:nth-child(3) { animation-delay: 0.7s; }

        .mode-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: rotate 4s linear infinite;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .mode-card:hover::before {
            opacity: 1;
        }

        .mode-card::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            background: linear-gradient(135deg, rgba(12, 12, 12, 0.9), rgba(26, 26, 46, 0.9));
            border-radius: 23px;
            z-index: -1;
        }

        .mode-card:hover {
            transform: translateY(-15px) scale(1.02);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
        }

        .mode-icon {
            font-size: 4rem;
            margin-bottom: 25px;
            display: block;
            text-align: center;
            transition: transform 0.4s ease;
            position: relative;
            z-index: 1;
        }

        .mode-card:hover .mode-icon {
            transform: scale(1.2) rotateY(10deg);
        }

        .mode-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
            position: relative;
            z-index: 1;
            font-family: 'Georgia', serif;
        }

        .mode-description {
            color: #ccc;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 25px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .mode-features {
            list-style: none;
            padding: 0;
            position: relative;
            z-index: 1;
        }

        .mode-features li {
            color: #b0b0b0;
            font-size: 0.9rem;
            margin-bottom: 8px;
            padding-left: 25px;
            position: relative;
        }

        .mode-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4ecdc4;
            font-weight: bold;
        }

        .difficulty-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            z-index: 2;
        }

        .classic-mode {
            background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), rgba(78, 205, 196, 0.05));
        }

        .classic-mode .difficulty-badge {
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
        }

        .survival-mode {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.05));
        }

        .survival-mode .difficulty-badge {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
        }

        .time-challenge {
            background: linear-gradient(135deg, rgba(69, 183, 209, 0.1), rgba(69, 183, 209, 0.05));
        }

        .time-challenge .difficulty-badge {
            background: rgba(69, 183, 209, 0.2);
            color: #45b7d1;
        }

        .selected {
            animation: selectedPulse 1.5s ease-in-out;
            border-color: rgba(255, 255, 255, 0.6) !important;
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.3) !important;
        }

        /* Letter floating animation */
        .letter-burst {
            position: absolute;
            pointer-events: none;
            z-index: 1000;
        }

        .burst-letter {
            position: absolute;
            font-size: 2rem;
            font-weight: bold;
            animation: burstOut 1s ease-out forwards;
        }

        /* Keyframe Animations */
        @keyframes letterFloat {
            0% {
                transform: translateY(100vh) rotate(0deg) scale(0.8);
                opacity: 0;
            }
            10% {
                opacity: 0.7;
            }
            90% {
                opacity: 0.7;
            }
            100% {
                transform: translateY(-100px) rotate(360deg) scale(1.2);
                opacity: 0;
            }
        }

        @keyframes wordDrift {
            0% {
                transform: translateX(-100px) rotate(-5deg);
                opacity: 0;
            }
            50% {
                opacity: 0.3;
            }
            100% {
                transform: translateX(calc(100vw + 100px)) rotate(5deg);
                opacity: 0;
            }
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes iconBounce {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            25% { transform: translateY(-10px) rotate(-5deg); }
            50% { transform: translateY(0) rotate(0deg); }
            75% { transform: translateY(-5px) rotate(5deg); }
        }

        @keyframes slideInTop {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes cardReveal {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes rotate {
            to { transform: rotate(360deg); }
        }

        @keyframes selectedPulse {
            0%, 100% { transform: translateY(-15px) scale(1.02); }
            50% { transform: translateY(-20px) scale(1.05); }
        }

        @keyframes burstOut {
            0% {
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: scale(0.3) rotate(360deg) translateY(-100px);
                opacity: 0;
            }
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        @keyframes fadeOut {
            to {
                opacity: 0;
                transform: scale(0.9);
            }
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .modes-container {
                grid-template-columns: 1fr;
                gap: 20px;
                margin-top: 30px;
            }

            .mode-card {
                padding: 30px 20px;
            }

            .back-button {
                position: relative;
                margin-bottom: 20px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 15px;
            }

            .mode-card {
                padding: 25px 15px;
            }

            .mode-icon {
                font-size: 3rem;
            }

            .mode-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="word-background" id="wordBackground"></div>

    <div class="container">
        <button class="back-button" onclick="goBack()">← Back to Categories</button>

        <div class="header">
            <h1 class="title">
                <span class="title-icon">📝</span>
                Wordplay Riddles
            </h1>
            <p class="subtitle">Master the Art of Language</p>
            <p class="description">Dive into the fascinating world of wordplay with puns, anagrams, palindromes, and linguistic puzzles that will twist your mind and expand your vocabulary.</p>
        </div>

        <div class="modes-container">
            <div class="mode-card classic-mode" data-mode="classic">
                <span class="difficulty-badge">Relaxed</span>
                <span class="mode-icon">📚</span>
                <h2 class="mode-title">Classic Mode</h2>
                <p class="mode-description">Explore wordplay riddles at your own pace. Perfect for learning new word tricks and expanding your linguistic horizons.</p>
                <ul class="mode-features">
                    <li>No time pressure</li>
                    <li>Etymology hints available</li>
                    <li>Progressive word difficulty</li>
                    <li>Detailed word explanations</li>
                    <li>Vocabulary building</li>
                </ul>
            </div>

            <div class="mode-card survival-mode" data-mode="survival">
                <span class="difficulty-badge">Intense</span>
                <span class="mode-icon">🔥</span>
                <h2 class="mode-title">Survival Mode</h2>
                <p class="mode-description">Face increasingly complex wordplay challenges with limited chances. Every wrong answer counts against you!</p>
                <ul class="mode-features">
                    <li>3 lives only</li>
                    <li>Escalating word complexity</li>
                    <li>No hints allowed</li>
                    <li>High score tracking</li>
                    <li>Wordsmith achievements</li>
                </ul>
            </div>

            <div class="mode-card time-challenge" data-mode="time-challenge">
                <span class="difficulty-badge">Lightning</span>
                <span class="mode-icon">⚡</span>
                <h2 class="mode-title">Speed Wordplay</h2>
                <p class="mode-description">Race against time to solve rapid-fire word puzzles. Quick thinking and vocabulary mastery are essential!</p>
                <ul class="mode-features">
                    <li>60-second lightning rounds</li>
                    <li>Quick-fire word challenges</li>
                    <li>Vocabulary streak bonuses</li>
                    <li>Global leaderboard</li>
                    <li>Speed reading rewards</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Letters and words for background animation
        const letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
        const words = ['RIDDLE', 'PUZZLE', 'ENIGMA', 'ANAGRAM', 'PALINDROME', 'PUNS', 'WORDS', 'LETTERS', 'MYSTERY', 'CIPHER', 'CODE', 'LANGUAGE', 'VERBAL', 'LEXICON'];

        function createWordBackground() {
            const background = document.getElementById('wordBackground');
            
            // Create floating letters
            for (let i = 0; i < 20; i++) {
                const letter = document.createElement('div');
                letter.className = 'floating-letter';
                letter.textContent = letters[Math.floor(Math.random() * letters.length)];
                letter.style.left = Math.random() * 100 + 'vw';
                letter.style.animationDelay = Math.random() * 20 + 's';
                letter.style.animationDuration = (Math.random() * 15 + 15) + 's';
                background.appendChild(letter);
            }

            // Create floating words
            for (let i = 0; i < 8; i++) {
                const word = document.createElement('div');
                word.className = 'floating-word';
                word.textContent = words[Math.floor(Math.random() * words.length)];
                word.style.top = Math.random() * 100 + 'vh';
                word.style.animationDelay = Math.random() * 30 + 's';
                word.style.animationDuration = (Math.random() * 20 + 25) + 's';
                background.appendChild(word);
            }
        }

        function createLetterBurst(x, y) {
            const burst = document.createElement('div');
            burst.className = 'letter-burst';
            burst.style.left = x + 'px';
            burst.style.top = y + 'px';
            document.body.appendChild(burst);

            // Create multiple letters bursting out
            for (let i = 0; i < 6; i++) {
                const letter = document.createElement('div');
                letter.className = 'burst-letter';
                letter.textContent = letters[Math.floor(Math.random() * letters.length)];
                letter.style.color = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#ffa726'][Math.floor(Math.random() * 4)];
                
                const angle = (Math.PI * 2 * i) / 6;
                const distance = 50 + Math.random() * 30;
                letter.style.left = Math.cos(angle) * distance + 'px';
                letter.style.top = Math.sin(angle) * distance + 'px';
                
                burst.appendChild(letter);
            }

            // Remove after animation
            setTimeout(() => {
                burst.remove();
            }, 1000);
        }

        function initializeModeCards() {
            const modeCards = document.querySelectorAll('.mode-card');
            
            modeCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    const mode = this.getAttribute('data-mode');
                    
                    // Add selection animation
                    this.classList.add('selected');
                    
                    // Create letter burst effect
                    const rect = this.getBoundingClientRect();
                    createLetterBurst(rect.left + rect.width / 2, rect.top + rect.height / 2);
                    
                    // Create selection ripple
                    const ripple = document.createElement('div');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(255, 255, 255, 0.3)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.left = '50%';
                    ripple.style.top = '50%';
                    ripple.style.width = '20px';
                    ripple.style.height = '20px';
                    ripple.style.marginLeft = '-10px';
                    ripple.style.marginTop = '-10px';
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        const modeName = this.querySelector('.mode-title').textContent;
                        alert(`📝 Loading ${modeName}...`);
                        
                        // Clean up
                        this.classList.remove('selected');
                        ripple.remove();
                    }, 1500);
                });

                // Add hover effects
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-15px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('selected')) {
                        this.style.transform = 'translateY(0) scale(1)';
                    }
                });
            });
        }

        function goBack() {
            // Add exit animation
            document.body.style.animation = 'fadeOut 0.5s ease-out forwards';
            setTimeout(() => {
                alert('🔙 Returning to category selection...');
                // Here you would navigate back to the category page
            }, 500);
        }

        // Add typewriter effect to title
        function typewriterEffect() {
            const title = document.querySelector('.title');
            const titleText = title.textContent;
            title.textContent = '';
            
            let i = 0;
            const typing = setInterval(() => {
                if (i < titleText.length) {
                    title.textContent += titleText.charAt(i);
                    i++;
                } else {
                    clearInterval(typing);
                }
            }, 100);
        }

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            createWordBackground();
            initializeModeCards();
            
            // Add continuous letter generation
            setInterval(() => {
                if (Math.random() < 0.3) {
                    const background = document.getElementById('wordBackground');
                    const letter = document.createElement('div');
                    letter.className = 'floating-letter';
                    letter.textContent = letters[Math.floor(Math.random() * letters.length)];
                    letter.style.left = Math.random() * 100 + 'vw';
                    letter.style.animationDuration = (Math.random() * 10 + 15) + 's';
                    background.appendChild(letter);
                    
                    // Remove after animation
                    setTimeout(() => {
                        if (letter.parentNode) {
                            letter.remove();
                        }
                    }, 25000);
                }
            }, 3000);
        });

        // Add interactive letter effects on mouse movement
        document.addEventListener('mousemove', function(e) {
            const letters = document.querySelectorAll('.floating-letter');
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;
            
            letters.forEach((letter, index) => {
                const speed = (index % 4 + 1) * 0.2;
                const x = (mouseX - 0.5) * speed * 20;
                const y = (mouseY - 0.5) * speed * 20;
                
                letter.style.transform += ` translate(${x}px, ${y}px)`;
            });

            // Randomly create small letter bursts near cursor
            if (Math.random() < 0.02) {
                createLetterBurst(e.clientX, e.clientY);
            }
        });

        // Add keyboard interaction
        document.addEventListener('keydown', function(e) {
            if (e.key.match(/[a-zA-Z]/)) {
                const x = Math.random() * window.innerWidth;
                const y = Math.random() * window.innerHeight;
                createLetterBurst(x, y);
            }
        });
    </script>
</body>
</html>