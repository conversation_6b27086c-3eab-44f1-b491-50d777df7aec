<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Math & Logic Riddles - Choose Your Mode</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            color: #fff;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated mathematical background */
        .math-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.05;
        }

        .math-symbol {
            position: absolute;
            font-size: clamp(1rem, 3vw, 2rem);
            color: #ff6b6b;
            animation: mathFloat 15s infinite linear;
        }

        .math-symbol:nth-child(even) {
            color: #4ecdc4;
            animation-direction: reverse;
        }

        .math-symbol:nth-child(3n) {
            color: #45b7d1;
            animation-duration: 20s;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            justify-content: center;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: slideInTop 1s ease-out;
        }

        .back-button {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            animation: fadeIn 1.5s ease-out;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
        }

        .title {
            font-size: clamp(2.5rem, 8vw, 4rem);
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .title-icon {
            font-size: clamp(2rem, 6vw, 3rem);
            animation: iconSpin 4s ease-in-out infinite;
        }

        .subtitle {
            font-size: clamp(1.2rem, 4vw, 1.8rem);
            color: #b0b0b0;
            margin-bottom: 15px;
            animation: fadeIn 1.5s ease-out;
        }

        .description {
            font-size: clamp(1rem, 2.5vw, 1.2rem);
            color: #888;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
            animation: fadeIn 2s ease-out;
        }

        .modes-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
            animation: slideUp 1s ease-out 0.8s both;
        }

        .mode-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 40px 30px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
            transform: translateY(30px);
            opacity: 0;
            animation: cardReveal 0.8s ease-out forwards;
        }

        .mode-card:nth-child(1) { animation-delay: 0.3s; }
        .mode-card:nth-child(2) { animation-delay: 0.5s; }
        .mode-card:nth-child(3) { animation-delay: 0.7s; }

        .mode-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: rotate 3s linear infinite;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .mode-card:hover::before {
            opacity: 1;
        }

        .mode-card::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            background: linear-gradient(135deg, rgba(12, 12, 12, 0.9), rgba(26, 26, 46, 0.9));
            border-radius: 23px;
            z-index: -1;
        }

        .mode-card:hover {
            transform: translateY(-15px) scale(1.02);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
        }

        .mode-icon {
            font-size: 4rem;
            margin-bottom: 25px;
            display: block;
            text-align: center;
            transition: transform 0.4s ease;
            position: relative;
            z-index: 1;
        }

        .mode-card:hover .mode-icon {
            transform: scale(1.2) rotateY(10deg);
        }

        .mode-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .mode-description {
            color: #ccc;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 25px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .mode-features {
            list-style: none;
            padding: 0;
            position: relative;
            z-index: 1;
        }

        .mode-features li {
            color: #b0b0b0;
            font-size: 0.9rem;
            margin-bottom: 8px;
            padding-left: 25px;
            position: relative;
        }

        .mode-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4ecdc4;
            font-weight: bold;
        }

        .difficulty-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            z-index: 2;
        }

        .classic-mode {
            background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), rgba(78, 205, 196, 0.05));
        }

        .classic-mode .difficulty-badge {
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
        }

        .survival-mode {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.05));
        }

        .survival-mode .difficulty-badge {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
        }

        .time-challenge {
            background: linear-gradient(135deg, rgba(69, 183, 209, 0.1), rgba(69, 183, 209, 0.05));
        }

        .time-challenge .difficulty-badge {
            background: rgba(69, 183, 209, 0.2);
            color: #45b7d1;
        }

        .selected {
            animation: selectedPulse 1.5s ease-in-out;
            border-color: rgba(255, 255, 255, 0.6) !important;
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.3) !important;
        }

        /* Keyframe Animations */
        @keyframes mathFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.5;
            }
            90% {
                opacity: 0.5;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes iconSpin {
            0%, 100% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(90deg) scale(1.1); }
            50% { transform: rotate(180deg) scale(1); }
            75% { transform: rotate(270deg) scale(1.1); }
        }

        @keyframes slideInTop {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes cardReveal {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes rotate {
            to { transform: rotate(360deg); }
        }

        @keyframes selectedPulse {
            0%, 100% { transform: translateY(-15px) scale(1.02); }
            50% { transform: translateY(-20px) scale(1.05); }
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .modes-container {
                grid-template-columns: 1fr;
                gap: 20px;
                margin-top: 30px;
            }

            .mode-card {
                padding: 30px 20px;
            }

            .stats-bar {
                gap: 20px;
                flex-wrap: wrap;
            }

            .stat-item {
                padding: 10px 15px;
                min-width: 100px;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .back-button {
                position: relative;
                margin-bottom: 20px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 15px;
            }

            .mode-card {
                padding: 25px 15px;
            }

            .mode-icon {
                font-size: 3rem;
            }

            .mode-title {
                font-size: 1.5rem;
            }

            .stats-bar {
                gap: 15px;
            }

            .stat-item {
                padding: 8px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="math-background" id="mathBackground"></div>

    <div class="container">
        <button class="back-button" onclick="goBack()">← Back to Categories</button>

        <div class="header">
            <h1 class="title">
                <span class="title-icon">🧮</span>
                Math & Logic
            </h1>
            <p class="subtitle">Challenge Your Analytical Mind</p>
            <p class="description">Test your mathematical reasoning and logical thinking with carefully crafted puzzles that will push your problem-solving skills to the limit.</p>
        </div>

        <div class="modes-container">
            <div class="mode-card classic-mode" data-mode="classic">
                <span class="mode-icon">🎯</span>
                <h2 class="mode-title">Classic Mode</h2>
                <p class="mode-description">Take your time to solve challenging math and logic puzzles at your own pace. Perfect for deep thinking and learning.</p>
                <ul class="mode-features">
                    <li>No time pressure</li>
                    <li>Hint system available</li>
                    <li>Progressive difficulty</li>
                    <li>Detailed explanations</li>
                    <li>Track your progress</li>
                </ul>
            </div>

            <div class="mode-card survival-mode" data-mode="survival">
                <span class="mode-icon">⚡</span>
                <h2 class="mode-title">Survival Mode</h2>
                <p class="mode-description">Face increasingly difficult puzzles with limited lives. One wrong answer brings you closer to elimination!</p>
                <ul class="mode-features">
                    <li>3 lives only</li>
                    <li>Escalating difficulty</li>
                    <li>No hints allowed</li>
                    <li>High score tracking</li>
                    <li>Achievement system</li>
                </ul>
            </div>

            <div class="mode-card time-challenge" data-mode="time-challenge">
                <span class="mode-icon">⏱️</span>
                <h2 class="mode-title">Time Challenge</h2>
                <p class="mode-description">Race against the clock! Solve as many puzzles as possible within the time limit. Speed and accuracy are key.</p>
                <ul class="mode-features">
                    <li>60-second rounds</li>
                    <li>Quick-fire questions</li>
                    <li>Combo multipliers</li>
                    <li>Global leaderboard</li>
                    <li>Streak bonuses</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Mathematical symbols for background animation
        const mathSymbols = ['∑', '∫', '∆', '∞', 'π', '√', '∂', '∇', 'Ω', 'α', 'β', 'γ', 'θ', 'λ', '±', '≠', '≤', '≥', '∈', '∉', '⊂', '⊃', '∪', '∩', '∅', '∃', '∀'];

        function createMathBackground() {
            const background = document.getElementById('mathBackground');
            
            // Create floating math symbols
            for (let i = 0; i < 15; i++) {
                const symbol = document.createElement('div');
                symbol.className = 'math-symbol';
                symbol.textContent = mathSymbols[Math.floor(Math.random() * mathSymbols.length)];
                symbol.style.left = Math.random() * 100 + 'vw';
                symbol.style.animationDelay = Math.random() * 15 + 's';
                symbol.style.animationDuration = (Math.random() * 10 + 10) + 's';
                background.appendChild(symbol);
            }
        }

        function animateStats() {
            const stats = [
                { element: document.getElementById('totalRiddles'), target: 150, suffix: '+' },
                { element: document.getElementById('difficulty'), target: 4, suffix: '/5' },
                { element: document.getElementById('avgTime'), target: 3, suffix: 'm' }
            ];

            stats.forEach(stat => {
                let current = 0;
                const increment = stat.target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= stat.target) {
                        current = stat.target;
                        clearInterval(timer);
                    }
                    stat.element.textContent = Math.floor(current) + stat.suffix;
                }, 30);
            });
        }

        function initializeModeCards() {
            const modeCards = document.querySelectorAll('.mode-card');
            
            modeCards.forEach(card => {
                card.addEventListener('click', function() {
                    const mode = this.getAttribute('data-mode');
                    
                    // Add selection animation
                    this.classList.add('selected');
                    
                    // Create selection effect
                    const ripple = document.createElement('div');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(255, 255, 255, 0.3)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.left = '50%';
                    ripple.style.top = '50%';
                    ripple.style.width = '20px';
                    ripple.style.height = '20px';
                    ripple.style.marginLeft = '-10px';
                    ripple.style.marginTop = '-10px';
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        const modeName = this.querySelector('.mode-title').textContent;
                        alert(`🚀 Loading ${modeName}...`);
                        
                        // Clean up
                        this.classList.remove('selected');
                        ripple.remove();
                    }, 1500);
                });

                // Add hover effects
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-15px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('selected')) {
                        this.style.transform = 'translateY(0) scale(1)';
                    }
                });
            });
        }

        function goBack() {
            // Add exit animation
            document.body.style.animation = 'fadeOut 0.5s ease-out forwards';
            setTimeout(() => {
                alert('🔙 Returning to category selection...');
                // Here you would navigate back to the category page
            }, 500);
        }

        // Add ripple animation to CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
            
            @keyframes fadeOut {
                to {
                    opacity: 0;
                    transform: scale(0.9);
                }
            }
        `;
        document.head.appendChild(style);

        // Initialize everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            createMathBackground();
            setTimeout(animateStats, 1000);
            initializeModeCards();
        });

        // Add parallax effect for math symbols
        document.addEventListener('mousemove', function(e) {
            const symbols = document.querySelectorAll('.math-symbol');
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;
            
            symbols.forEach((symbol, index) => {
                const speed = (index % 3 + 1) * 0.3;
                const x = (mouseX - 0.5) * speed * 30;
                const y = (mouseY - 0.5) * speed * 30;
                
                symbol.style.transform += ` translate(${x}px, ${y}px)`;
            });
        });
    </script>
</body>
</html>