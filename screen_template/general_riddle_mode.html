<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>General Riddles - Choose Your Mode</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Trebuchet MS', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            color: #fff;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated mystery background */
        .mystery-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.06;
        }

        .floating-symbol {
            position: absolute;
            font-size: clamp(1.5rem, 4vw, 3.5rem);
            color: #ff6b6b;
            animation: mysteryFloat 18s infinite linear;
            text-shadow: 0 0 15px rgba(255, 107, 107, 0.4);
            filter: blur(0.5px);
        }

        .floating-symbol:nth-child(even) {
            color: #4ecdc4;
            animation-direction: reverse;
            text-shadow: 0 0 15px rgba(78, 205, 196, 0.4);
            animation-duration: 22s;
        }

        .floating-symbol:nth-child(3n) {
            color: #45b7d1;
            animation-duration: 25s;
            text-shadow: 0 0 15px rgba(69, 183, 209, 0.4);
        }

        .floating-symbol:nth-child(4n) {
            color: #ffa726;
            animation-duration: 20s;
            text-shadow: 0 0 15px rgba(255, 167, 38, 0.4);
        }

        .floating-symbol:nth-child(5n) {
            color: #ab47bc;
            animation-duration: 16s;
            text-shadow: 0 0 15px rgba(171, 71, 188, 0.4);
        }

        .mystery-orb {
            position: absolute;
            width: clamp(20px, 3vw, 40px);
            height: clamp(20px, 3vw, 40px);
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 107, 107, 0.3), transparent);
            animation: orbFloat 12s infinite ease-in-out;
            filter: blur(1px);
        }

        .mystery-orb:nth-child(even) {
            background: radial-gradient(circle, rgba(78, 205, 196, 0.3), transparent);
            animation-direction: reverse;
            animation-duration: 15s;
        }

        .riddle-word {
            position: absolute;
            font-size: clamp(0.9rem, 2.5vw, 1.4rem);
            color: rgba(255, 255, 255, 0.08);
            animation: riddleWordDrift 35s infinite linear;
            font-style: italic;
            font-weight: 300;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            justify-content: center;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: slideInTop 1s ease-out;
        }

        .back-button {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            animation: fadeIn 1.5s ease-out;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
            box-shadow: 0 5px 15px rgba(255, 255, 255, 0.1);
        }

        .title {
            font-size: clamp(2.5rem, 8vw, 4rem);
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #ffa726, #ab47bc);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 5s ease-in-out infinite;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.1);
        }

        .title-icon {
            font-size: clamp(2rem, 6vw, 3rem);
            animation: iconMystery 4s ease-in-out infinite;
            filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
        }

        .subtitle {
            font-size: clamp(1.2rem, 4vw, 1.8rem);
            color: #b0b0b0;
            margin-bottom: 15px;
            animation: fadeIn 1.5s ease-out;
            font-style: italic;
            text-shadow: 0 0 10px rgba(176, 176, 176, 0.3);
        }

        .description {
            font-size: clamp(1rem, 2.5vw, 1.2rem);
            color: #888;
            max-width: 700px;
            margin: 0 auto;
            line-height: 1.6;
            animation: fadeIn 2s ease-out;
        }

        .modes-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
            animation: slideUp 1s ease-out 0.8s both;
        }

        .mode-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 40px 30px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
            transform: translateY(30px);
            opacity: 0;
            animation: cardReveal 0.8s ease-out forwards;
        }

        .mode-card:nth-child(1) { animation-delay: 0.3s; }
        .mode-card:nth-child(2) { animation-delay: 0.5s; }
        .mode-card:nth-child(3) { animation-delay: 0.7s; }

        .mode-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: rotate 5s linear infinite;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .mode-card:hover::before {
            opacity: 1;
        }

        .mode-card::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            background: linear-gradient(135deg, rgba(12, 12, 12, 0.9), rgba(26, 26, 46, 0.9));
            border-radius: 23px;
            z-index: -1;
        }

        .mode-card:hover {
            transform: translateY(-15px) scale(1.02);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 255, 255, 0.1);
        }

        .mode-icon {
            font-size: 4rem;
            margin-bottom: 25px;
            display: block;
            text-align: center;
            transition: transform 0.4s ease;
            position: relative;
            z-index: 1;
            filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.2));
        }

        .mode-card:hover .mode-icon {
            transform: scale(1.2) rotateY(15deg);
            filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.4));
        }

        .mode-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .mode-description {
            color: #ccc;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 25px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .mode-features {
            list-style: none;
            padding: 0;
            position: relative;
            z-index: 1;
        }

        .mode-features li {
            color: #b0b0b0;
            font-size: 0.9rem;
            margin-bottom: 8px;
            padding-left: 25px;
            position: relative;
        }

        .mode-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4ecdc4;
            font-weight: bold;
            text-shadow: 0 0 5px rgba(78, 205, 196, 0.5);
        }

        .difficulty-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            z-index: 2;
            backdrop-filter: blur(5px);
        }

        .classic-mode {
            background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), rgba(78, 205, 196, 0.05));
        }

        .classic-mode .difficulty-badge {
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
            border: 1px solid rgba(78, 205, 196, 0.3);
        }

        .survival-mode {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.05));
        }

        .survival-mode .difficulty-badge {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }

        .time-challenge {
            background: linear-gradient(135deg, rgba(69, 183, 209, 0.1), rgba(69, 183, 209, 0.05));
        }

        .time-challenge .difficulty-badge {
            background: rgba(69, 183, 209, 0.2);
            color: #45b7d1;
            border: 1px solid rgba(69, 183, 209, 0.3);
        }

        .selected {
            animation: selectedPulse 1.5s ease-in-out;
            border-color: rgba(255, 255, 255, 0.6) !important;
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.3) !important;
        }

        /* Mystery particle effects */
        .mystery-particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: #fff;
            border-radius: 50%;
            animation: particleFloat 8s infinite ease-in-out;
            opacity: 0.6;
        }

        /* Keyframe Animations */
        @keyframes mysteryFloat {
            0% {
                transform: translateY(100vh) rotate(0deg) scale(0.5);
                opacity: 0;
            }
            10% {
                opacity: 0.8;
            }
            90% {
                opacity: 0.8;
            }
            100% {
                transform: translateY(-100px) rotate(720deg) scale(1.5);
                opacity: 0;
            }
        }

        @keyframes orbFloat {
            0%, 100% {
                transform: translateY(0) scale(1);
                opacity: 0.3;
            }
            50% {
                transform: translateY(-20px) scale(1.2);
                opacity: 0.7;
            }
        }

        @keyframes riddleWordDrift {
            0% {
                transform: translateX(-150px) rotate(-2deg);
                opacity: 0;
            }
            50% {
                opacity: 0.4;
            }
            100% {
                transform: translateX(calc(100vw + 150px)) rotate(2deg);
                opacity: 0;
            }
        }

        @keyframes particleFloat {
            0%, 100% {
                transform: translateY(0) translateX(0);
                opacity: 0.6;
            }
            25% {
                transform: translateY(-15px) translateX(10px);
                opacity: 1;
            }
            50% {
                transform: translateY(-8px) translateX(-5px);
                opacity: 0.8;
            }
            75% {
                transform: translateY(-20px) translateX(15px);
                opacity: 0.9;
            }
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes iconMystery {
            0%, 100% { 
                transform: rotate(0deg) scale(1);
                filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
            }
            25% { 
                transform: rotate(-10deg) scale(1.1);
                filter: drop-shadow(0 0 15px rgba(255, 107, 107, 0.4));
            }
            50% { 
                transform: rotate(0deg) scale(1.05);
                filter: drop-shadow(0 0 20px rgba(78, 205, 196, 0.4));
            }
            75% { 
                transform: rotate(10deg) scale(1.1);
                filter: drop-shadow(0 0 15px rgba(69, 183, 209, 0.4));
            }
        }

        @keyframes slideInTop {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes cardReveal {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes rotate {
            to { transform: rotate(360deg); }
        }

        @keyframes selectedPulse {
            0%, 100% { 
                transform: translateY(-15px) scale(1.02);
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 255, 255, 0.3);
            }
            50% { 
                transform: translateY(-20px) scale(1.05);
                box-shadow: 0 30px 60px rgba(0, 0, 0, 0.5), 0 0 40px rgba(255, 255, 255, 0.4);
            }
        }

        @keyframes symbolBurst {
            0% {
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: scale(0.2) rotate(360deg) translateY(-120px);
                opacity: 0;
            }
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        @keyframes fadeOut {
            to {
                opacity: 0;
                transform: scale(0.9);
            }
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .modes-container {
                grid-template-columns: 1fr;
                gap: 20px;
                margin-top: 30px;
            }

            .mode-card {
                padding: 30px 20px;
            }

            .back-button {
                position: relative;
                margin-bottom: 20px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 15px;
            }

            .mode-card {
                padding: 25px 15px;
            }

            .mode-icon {
                font-size: 3rem;
            }

            .mode-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="mystery-background" id="mysteryBackground"></div>

    <div class="container">
        <button class="back-button" onclick="goBack()">← Back to Categories</button>

        <div class="header">
            <h1 class="title">
                <span class="title-icon">🔮</span>
                General Riddles
            </h1>
            <p class="subtitle">Unlock the Mysteries of Mind</p>
            <p class="description">Journey through a diverse collection of brain-teasing riddles that challenge logic, creativity, and lateral thinking. From ancient puzzles to modern conundrums, test your wit against the unknown.</p>
        </div>

        <div class="modes-container">
            <div class="mode-card classic-mode" data-mode="classic">
                <span class="difficulty-badge">Thoughtful</span>
                <span class="mode-icon">🎭</span>
                <h2 class="mode-title">Classic Mode</h2>
                <p class="mode-description">Explore timeless riddles at your own pace. Perfect for pondering life's mysteries and expanding your problem-solving toolkit.</p>
                <ul class="mode-features">
                    <li>No time pressure</li>
                    <li>Contextual hints available</li>
                    <li>Progressive complexity</li>
                    <li>Detailed solution breakdowns</li>
                    <li>Riddle history and origins</li>
                </ul>
            </div>

            <div class="mode-card survival-mode" data-mode="survival">
                <span class="difficulty-badge">Perilous</span>
                <span class="mode-icon">🌟</span>
                <h2 class="mode-title">Survival Mode</h2>
                <p class="mode-description">Navigate through increasingly challenging riddles with limited chances. Each mistake brings you closer to the edge of defeat!</p>
                <ul class="mode-features">
                    <li>3 lives only</li>
                    <li>Escalating difficulty curve</li>
                    <li>No hints allowed</li>
                    <li>Master riddler rankings</li>
                    <li>Legendary achievement badges</li>
                </ul>
            </div>

            <div class="mode-card time-challenge" data-mode="time-challenge">
                <span class="difficulty-badge">Frantic</span>
                <span class="mode-icon">⚡</span>
                <h2 class="mode-title">Lightning Riddles</h2>
                <p class="mode-description">Race against time to solve rapid-fire riddles! Quick thinking and instant insights are your keys to victory.</p>
                <ul class="mode-features">
                    <li>60-second lightning rounds</li>
                    <li>Rapid-fire brain teasers</li>
                    <li>Insight streak multipliers</li>
                    <li>Global hall of fame</li>
                    <li>Speed-thinking rewards</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // Mystery symbols and elements for background animation
const mysterySymbols = ['?', '!', '❓', '❗', '💡', '🔍', '🗝️', '🧩', '⭐', '💎', '🎯', '🌟', '✨', '🔮', '👁️', '🎭', '🌙', '☀️', '⚡', '💫'];
const riddleWords = ['MYSTERY', 'ENIGMA', 'PUZZLE', 'RIDDLE', 'QUESTION', 'ANSWER', 'THINK', 'SOLVE', 'CLUE', 'SECRET', 'UNKNOWN', 'DISCOVER', 'REVEAL', 'TRUTH', 'WONDER'];

function createMysteryBackground() {
    const background = document.getElementById('mysteryBackground');
    
    // Create floating mystery symbols
    for (let i = 0; i < 18; i++) {
        const symbol = document.createElement('div');
        symbol.className = 'floating-symbol';
        symbol.textContent = mysterySymbols[Math.floor(Math.random() * mysterySymbols.length)];
        symbol.style.left = Math.random() * 100 + 'vw';
        symbol.style.animationDelay = Math.random() * 18 + 's';
        symbol.style.animationDuration = (Math.random() * 10 + 15) + 's';
        background.appendChild(symbol);
    }

    // Create mystery orbs
    for (let i = 0; i < 8; i++) {
        const orb = document.createElement('div');
        orb.className = 'mystery-orb';
        orb.style.left = Math.random() * 100 + 'vw';
        orb.style.top = Math.random() * 100 + 'vh';
        orb.style.animationDelay = Math.random() * 12 + 's';
        background.appendChild(orb);
    }

    // Create floating riddle words
    for (let i = 0; i < 6; i++) {
        const word = document.createElement('div');
        word.className = 'riddle-word';
        word.textContent = riddleWords[Math.floor(Math.random() * riddleWords.length)];
        word.style.top = Math.random() * 100 + 'vh';
        word.style.animationDelay = Math.random() * 35 + 's';
        word.style.animationDuration = (Math.random() * 15 + 30) + 's';
        background.appendChild(word);
    }

    // Create floating particles
    for (let i = 0; i < 25; i++) {
        const particle = document.createElement('div');
        particle.className = 'mystery-particle';
        particle.style.left = Math.random() * 100 + 'vw';
        particle.style.top = Math.random() * 100 + 'vh';
        particle.style.animationDelay = Math.random() * 8 + 's';
        particle.style.animationDuration = (Math.random() * 4 + 6) + 's';
        background.appendChild(particle);
    }
}

function createSymbolBurst(x, y) {
    const burst = document.createElement('div');
    burst.style.position = 'absolute';
    burst.style.left = x + 'px';
    burst.style.top = y + 'px';
    burst.style.pointerEvents = 'none';
    burst.style.zIndex = '1000';
    document.body.appendChild(burst);

    // Create multiple symbols bursting out
    for (let i = 0; i < 8; i++) {
        const symbol = document.createElement('div');
        symbol.textContent = mysterySymbols[Math.floor(Math.random() * mysterySymbols.length)];
        symbol.style.position = 'absolute';
        symbol.style.fontSize = '2rem';
        symbol.style.fontWeight = 'bold';
        symbol.style.color = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#ffa726', '#ab47bc'][Math.floor(Math.random() * 5)];
        symbol.style.animation = 'symbolBurst 1.2s ease-out forwards';
        symbol.style.textShadow = '0 0 10px currentColor';
        
        const angle = (Math.PI * 2 * i) / 8;
        const distance = 60 + Math.random() * 40;
        symbol.style.left = Math.cos(angle) * distance + 'px';
        symbol.style.top = Math.sin(angle) * distance + 'px';
        
        burst.appendChild(symbol);
    }

    // Remove after animation
    setTimeout(() => {
        burst.remove();
    }, 1200);
}

function createMysteryPulse(element) {
    const pulse = document.createElement('div');
    pulse.style.position = 'absolute';
    pulse.style.left = '50%';
    pulse.style.top = '50%';
    pulse.style.width = '100%';
    pulse.style.height = '100%';
    pulse.style.borderRadius = '25px';
    pulse.style.border = '2px solid rgba(255, 255, 255, 0.5)';
    pulse.style.transform = 'translate(-50%, -50%) scale(1)';
    pulse.style.animation = 'ripple 0.8s ease-out forwards';
    pulse.style.pointerEvents = 'none';
    element.appendChild(pulse);

    setTimeout(() => {
        pulse.remove();
    }, 800);
}

function initializeModeCards() {
    const modeCards = document.querySelectorAll('.mode-card');
    
    modeCards.forEach(card => {
        card.addEventListener('click', function(e) {
            const mode = this.getAttribute('data-mode');
            
            // Add selection animation
            this.classList.add('selected');
            
            // Create symbol burst effect
            const rect = this.getBoundingClientRect();
            createSymbolBurst(rect.left + rect.width / 2, rect.top + rect.height / 2);
            
            // Create mystery pulse
            createMysteryPulse(this);
            
            setTimeout(() => {
                const modeName = this.querySelector('.mode-title').textContent;
                alert(`🔮 Loading ${modeName}...`);
                
                // Clean up
                this.classList.remove('selected');
            }, 1500);
        });

        // Add hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-15px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            if (!this.classList.contains('selected')) {
                this.style.transform = 'translateY(0) scale(1)';
            }
        });
    });
}

function goBack() {
    // Add exit animation
    document.body.style.animation = 'fadeOut 0.5s ease-out forwards';
    setTimeout(() => {
        alert('🔙 Returning to category selection...');
        // Here you would navigate back to the category page
        // window.location.href = 'index.html'; // uncomment to actually navigate
    }, 500);
}

// Initialize everything when page loads
document.addEventListener('DOMContentLoaded', function() {
    createMysteryBackground();
    initializeModeCards();
    
    // Add continuous symbol generation
    setInterval(() => {
        if (Math.random() < 0.25) {
            const background = document.getElementById('mysteryBackground');
            const symbol = document.createElement('div');
            symbol.className = 'floating-symbol';
            symbol.textContent = mysterySymbols[Math.floor(Math.random() * mysterySymbols.length)];
            symbol.style.left = Math.random() * 100 + 'vw';
            symbol.style.animationDuration = (Math.random() * 8 + 15) + 's';
            background.appendChild(symbol);
            
            // Remove after animation
            setTimeout(() => {
                if (symbol.parentNode) {
                    symbol.remove();
                }
            }, 25000);
        }
    }, 4000);

    // Add continuous particle generation
    setInterval(() => {
        if (Math.random() < 0.4) {
            const background = document.getElementById('mysteryBackground');
            const particle = document.createElement('div');
            particle.className = 'mystery-particle';
            particle.style.left = Math.random() * 100 + 'vw';
            particle.style.top = Math.random() * 100 + 'vh';
            particle.style.animationDuration = (Math.random() * 3 + 5) + 's';
            background.appendChild(particle);
            
            // Complete the missing setTimeout
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.remove();
                }
            }, 8000);
        }
    }, 2000);

    // Add continuous riddle word generation
    setInterval(() => {
        if (Math.random() < 0.15) {
            const background = document.getElementById('mysteryBackground');
            const word = document.createElement('div');
            word.className = 'riddle-word';
            word.textContent = riddleWords[Math.floor(Math.random() * riddleWords.length)];
            word.style.top = Math.random() * 100 + 'vh';
            word.style.animationDuration = (Math.random() * 10 + 25) + 's';
            background.appendChild(word);
            
            setTimeout(() => {
                if (word.parentNode) {
                    word.remove();
                }
            }, 40000);
        }
    }, 8000);
});
</script>