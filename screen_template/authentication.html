<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Riddle Quest - Join the Adventure</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated background elements */
        .bg-decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }

        .floating-shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape1 {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape2 {
            width: 60px;
            height: 60px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .shape3 {
            width: 100px;
            height: 100px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        .container {
            background: rgba(20, 20, 35, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.5);
            width: 90%;
            max-width: 450px;
            position: relative;
            z-index: 1;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            color: #64ffda;
            font-size: 2.5rem;
            font-weight: bold;
            text-shadow: 0 0 20px rgba(100, 255, 218, 0.3);
            margin-bottom: 10px;
        }

        .logo p {
            color: #b0bec5;
            font-size: 1rem;
            font-style: italic;
        }

        .tab-buttons {
            display: flex;
            margin-bottom: 30px;
            background: #2a2a3e;
            border-radius: 12px;
            padding: 5px;
        }

        .tab-btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            color: #90a4ae;
        }

        .tab-btn.active {
            background: linear-gradient(135deg, #64ffda, #00bcd4);
            color: #1a1a2e;
            box-shadow: 0 4px 12px rgba(100, 255, 218, 0.3);
        }

        .form-container {
            display: none;
        }

        .form-container.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #eceff1;
            font-weight: 600;
        }

        .form-group input {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid #455a64;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #263238;
            color: #eceff1;
        }

        .form-group input:focus {
            outline: none;
            border-color: #64ffda;
            box-shadow: 0 0 0 3px rgba(100, 255, 218, 0.2);
            background: #37474f;
        }

        .form-group input::placeholder {
            color: #78909c;
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            cursor: pointer;
            color: #90a4ae;
            font-size: 18px;
            padding: 4px;
            border-radius: 4px;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: #64ffda;
        }

        .avatar-section {
            text-align: center;
            margin-bottom: 20px;
        }

        .avatar-preview {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            margin: 0 auto 15px;
            background: #2a2a3e;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 3px solid #455a64;
            overflow: hidden;
            position: relative;
        }

        .avatar-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .avatar-placeholder {
            color: #90a4ae;
            font-size: 40px;
        }

        .avatar-options {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 15px;
        }

        .avatar-btn {
            padding: 8px 16px;
            border: 2px solid #64ffda;
            background: transparent;
            color: #64ffda;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .avatar-btn:hover {
            background: #64ffda;
            color: #1a1a2e;
            box-shadow: 0 0 15px rgba(100, 255, 218, 0.3);
        }

        .preset-avatars {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 10px;
        }

        .preset-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            border: 3px solid transparent;
            transition: all 0.3s ease;
            background-size: cover;
            background-position: center;
        }

        .preset-avatar:hover {
            border-color: #64ffda;
            transform: scale(1.1);
            box-shadow: 0 0 15px rgba(100, 255, 218, 0.4);
        }

        .preset-avatar.selected {
            border-color: #64ffda;
            box-shadow: 0 0 0 2px rgba(100, 255, 218, 0.5);
        }

        .google-btn {
            width: 100%;
            padding: 14px;
            border: 2px solid #ea4335;
            background: transparent;
            color: #ea4335;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .google-btn:hover {
            background: #ea4335;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(234, 67, 53, 0.4);
        }

        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
            color: #78909c;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #455a64;
        }

        .divider span {
            background: rgba(20, 20, 35, 0.95);
            padding: 0 15px;
        }

        .submit-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #64ffda, #00bcd4);
            color: #1a1a2e;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(100, 255, 218, 0.3);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(100, 255, 218, 0.5);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        #file-input {
            display: none;
        }

        .error-message {
            color: #ff5252;
            font-size: 14px;
            margin-top: 5px;
            display: none;
            padding: 8px 12px;
            background: rgba(255, 82, 82, 0.1);
            border: 1px solid rgba(255, 82, 82, 0.3);
            border-radius: 8px;
            animation: slideDown 0.3s ease;
        }

        .success-message {
            color: #4caf50;
            font-size: 14px;
            margin-top: 5px;
            display: none;
            padding: 8px 12px;
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            border-radius: 8px;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .shake {
            animation: shake 0.5s ease-in-out;
        }

        .input-error {
            border-color: #ff5252 !important;
            box-shadow: 0 0 0 3px rgba(255, 82, 82, 0.2) !important;
        }

        .password-strength {
            margin-top: 8px;
            padding: 8px;
            border-radius: 6px;
            font-size: 12px;
            display: none;
        }

        .strength-weak {
            background: rgba(255, 82, 82, 0.1);
            color: #ff5252;
            border: 1px solid rgba(255, 82, 82, 0.3);
        }

        .strength-medium {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .strength-strong {
            background: rgba(76, 175, 80, 0.1);
            color: #4caf50;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            border: 2px solid #64ffda;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
            transform: translate(-50%, -50%);
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
                margin: 20px;
            }
            
            .logo h1 {
                font-size: 2rem;
            }
            
            .preset-avatars {
                gap: 8px;
            }
            
            .preset-avatar {
                width: 40px;
                height: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="bg-decoration">
        <div class="floating-shape shape1"></div>
        <div class="floating-shape shape2"></div>
        <div class="floating-shape shape3"></div>
    </div>

    <div class="container">
        <div class="logo">
            <h1>🎭 Riddle Quest</h1>
            <p>Truth, Dare & Mystery Await</p>
        </div>

        <div class="tab-buttons">
            <button class="tab-btn active" onclick="switchTab('signin')">Sign In</button>
            <button class="tab-btn" onclick="switchTab('signup')">Sign Up</button>
        </div>

        <!-- Sign In Form -->
        <div class="form-container active" id="signin-form">
            <button class="google-btn" onclick="signInWithGoogle()">
                <span>📧</span>
                Continue with Google
            </button>
            
            <div class="divider">
                <span>or</span>
            </div>

            <form onsubmit="handleSignIn(event)">
                <div class="form-group">
                    <label for="signin-email">Email Address</label>
                    <input type="email" id="signin-email" required>
                    <div class="error-message" id="signin-email-error"></div>
                </div>

                <div class="form-group">
                    <label for="signin-password">Password</label>
                    <div class="password-container">
                        <input type="password" id="signin-password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('signin-password')">
                            👁️
                        </button>
                    </div>
                    <div class="error-message" id="signin-password-error"></div>
                </div>

                <button type="submit" class="submit-btn">Enter the Game</button>
            </form>
        </div>

        <!-- Sign Up Form -->
        <div class="form-container" id="signup-form">
            <button class="google-btn" onclick="signUpWithGoogle()">
                <span>📧</span>
                Sign up with Google
            </button>
            
            <div class="divider">
                <span>or</span>
            </div>

            <form onsubmit="handleSignUp(event)">
                <div class="avatar-section">
                    <div class="avatar-preview" id="avatar-preview">
                        <span class="avatar-placeholder">👤</span>
                    </div>
                    
                    <div class="avatar-options">
                        <button type="button" class="avatar-btn" onclick="document.getElementById('file-input').click()">
                            Upload Photo
                        </button>
                        <button type="button" class="avatar-btn" onclick="togglePresetAvatars()">
                            Choose Avatar
                        </button>
                    </div>
                    
                    <input type="file" id="file-input" accept="image/*" onchange="handleImageUpload(event)">
                    
                    <div class="preset-avatars" id="preset-avatars" style="display: none;">
                        <div class="preset-avatar" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiNGRjZCNkIiLz4KPGV5ZXMgZmlsbD0iIzMzMzMzMyI+CjxjaXJjbGUgY3g9IjE4IiBjeT0iMjAiIHI9IjMiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyMCIgcj0iMyIvPgo8L2V5ZXM+CjxwYXRoIGQ9Ik0xNSAzMFEyNSAzNSAzNSAzMCIgc3Ryb2tlPSIjMzMzMzMzIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPC9zdmc+')" onclick="selectPresetAvatar(this, '🙂')"></div>
                        <div class="preset-avatar" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiM2QkM5RkYiLz4KPGV5ZXMgZmlsbD0iIzMzMzMzMyI+CjxjaXJjbGUgY3g9IjE4IiBjeT0iMjAiIHI9IjMiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyMCIgcj0iMyIvPgo8L2V5ZXM+CjxwYXRoIGQ9Ik0xNSAzMFEyNSAzNSAzNSAzMCIgc3Ryb2tlPSIjMzMzMzMzIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPC9zdmc+')" onclick="selectPresetAvatar(this, '😊')"></div>
                        <div class="preset-avatar" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiNGRkI4NkMiLz4KPGV5ZXMgZmlsbD0iIzMzMzMzMyI+CjxjaXJjbGUgY3g9IjE4IiBjeT0iMjAiIHI9IjMiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyMCIgcj0iMyIvPgo8L2V5ZXM+CjxwYXRoIGQ9Ik0xNSAzMFEyNSAzNSAzNSAzMCIgc3Ryb2tlPSIjMzMzMzMzIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPC9zdmc+')" onclick="selectPresetAvatar(this, '🤔')"></div>
                        <div class="preset-avatar" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiNBNzg2RkYiLz4KPGV5ZXMgZmlsbD0iIzMzMzMzMyI+CjxjaXJjbGUgY3g9IjE4IiBjeT0iMjAiIHI9IjMiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIyMCIgcj0iMyIvPgo8L2V5ZXM+CjxwYXRoIGQ9Ik0xNSAzMFEyNSAzNSAzNSAzMCIgc3Ryb2tlPSIjMzMzMzMzIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPC9zdmc+')" onclick="selectPresetAvatar(this, '😎')"></div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="signup-username">Username</label>
                    <input type="text" id="signup-username" required>
                    <div class="error-message" id="signup-username-error"></div>
                </div>

                <div class="form-group">
                    <label for="signup-email">Email Address</label>
                    <input type="email" id="signup-email" required>
                    <div class="error-message" id="signup-email-error"></div>
                </div>

                <div class="form-group">
                    <label for="signup-password">Password</label>
                    <div class="password-container">
                        <input type="password" id="signup-password" required oninput="checkPasswordStrength()">
                        <button type="button" class="password-toggle" onclick="togglePassword('signup-password')">
                            👁️
                        </button>
                    </div>
                    <div class="password-strength" id="password-strength"></div>
                    <div class="error-message" id="signup-password-error"></div>
                </div>

                <div class="form-group">
                    <label for="confirm-password">Confirm Password</label>
                    <div class="password-container">
                        <input type="password" id="confirm-password" required oninput="checkPasswordMatch()">
                        <button type="button" class="password-toggle" onclick="togglePassword('confirm-password')">
                            👁️
                        </button>
                    </div>
                    <div class="error-message" id="confirm-password-error"></div>
                </div>

                <button type="submit" class="submit-btn">Join the Adventure</button>
            </form>
        </div>
    </div>

    <script>
        let selectedAvatar = null;
        
       

        function switchTab(tab) {
            // Clear all errors when switching tabs
            clearAllErrors();
            
            // Update tab buttons
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Update form containers
            document.querySelectorAll('.form-container').forEach(container => container.classList.remove('active'));
            document.getElementById(tab + '-form').classList.add('active');
        }

        function clearAllErrors() {
            document.querySelectorAll('.error-message').forEach(error => {
                error.style.display = 'none';
            });
            document.querySelectorAll('.input-error').forEach(input => {
                input.classList.remove('input-error');
            });
            document.querySelectorAll('.shake').forEach(element => {
                element.classList.remove('shake');
            });
        }

        function showError(inputId, message) {
            const input = document.getElementById(inputId);
            const errorElement = document.getElementById(inputId + '-error');
            
            input.classList.add('input-error');
            input.parentElement.classList.add('shake');
            
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }
            
            // Remove shake animation after it completes
            setTimeout(() => {
                input.parentElement.classList.remove('shake');
            }, 5000);
        }

        function hideError(inputId) {
            const input = document.getElementById(inputId);
            const errorElement = document.getElementById(inputId + '-error');
            
            input.classList.remove('input-error');
            if (errorElement) {
                errorElement.style.display = 'none';
            }
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function checkPasswordStrength() {
            const password = document.getElementById('signup-password').value;
            const strengthElement = document.getElementById('password-strength');
            
            if (password.length === 0) {
                strengthElement.style.display = 'none';
                return;
            }
            
            let strength = 0;
            let feedback = [];
            
            // Length check
            if (password.length >= 6) strength++;
            else feedback.push('At least 6 characters');
            
            // Uppercase check
            if (/[A-Z]/.test(password)) strength++;
            else feedback.push('One uppercase letter');
            
            // Lowercase check
            if (/[a-z]/.test(password)) strength++;
            else feedback.push('One lowercase letter');
            
            // Number check
            if (/\d/.test(password)) strength++;
            else feedback.push('One number');
            
            // Special character check
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
            else feedback.push('One special character');
            
            let strengthText = '';
            let strengthClass = '';
            
            if (strength < 2) {
                strengthText = `Weak password. Add: ${feedback.slice(0, 3).join(', ')}`;
                strengthClass = 'strength-weak';
            } else if (strength < 4) {
                strengthText = `Medium strength. Consider adding: ${feedback.slice(0, 2).join(', ')}`;
                strengthClass = 'strength-medium';
            } else {
                strengthText = 'Strong password! 💪';
                strengthClass = 'strength-strong';
            }
            
            strengthElement.textContent = strengthText;
            strengthElement.className = `password-strength ${strengthClass}`;
            strengthElement.style.display = 'block';
            
            return strength >= 2; // Minimum acceptable strength
        }

        function checkPasswordMatch() {
            const password = document.getElementById('signup-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            
            if (confirmPassword.length > 0) {
                if (password !== confirmPassword) {
                    showError('confirm-password', 'Passwords do not match');
                    return false;
                } else {
                    hideError('confirm-password');
                    return true;
                }
            }
            return true;
        }

        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const button = input.nextElementSibling;
            
            if (input.type === 'password') {
                input.type = 'text';
                button.textContent = '🙈';
            } else {
                input.type = 'password';
                button.textContent = '👁️';
            }
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const avatarPreview = document.getElementById('avatar-preview');
                    avatarPreview.innerHTML = `<img src="${e.target.result}" alt="Avatar">`;
                    selectedAvatar = e.target.result;
                    
                    // Remove selection from preset avatars
                    document.querySelectorAll('.preset-avatar').forEach(avatar => {
                        avatar.classList.remove('selected');
                    });
                };
                reader.readAsDataURL(file);
            }
        }

        function togglePresetAvatars() {
            const presetAvatars = document.getElementById('preset-avatars');
            presetAvatars.style.display = presetAvatars.style.display === 'none' ? 'flex' : 'none';
        }

        function selectPresetAvatar(element, emoji) {
            // Remove previous selections
            document.querySelectorAll('.preset-avatar').forEach(avatar => {
                avatar.classList.remove('selected');
            });
            
            // Select current avatar
            element.classList.add('selected');
            
            // Update preview
            const avatarPreview = document.getElementById('avatar-preview');
            avatarPreview.innerHTML = `<span style="font-size: 60px;">${emoji}</span>`;
            selectedAvatar = emoji;
        }

        function signInWithGoogle() {
            // Simulate Google Sign In
            alert('🚀 Google Sign In would be implemented here!\n\nIn a real app, this would use Google OAuth API.');
        }

        function signUpWithGoogle() {
            // Simulate Google Sign Up
            alert('🚀 Google Sign Up would be implemented here!\n\nIn a real app, this would use Google OAuth API.');
        }

        function handleSignIn(event) {
            event.preventDefault();
            clearAllErrors();
            
            const email = document.getElementById('signin-email').value;
            const password = document.getElementById('signin-password').value;
            const submitBtn = event.target.querySelector('.submit-btn');
            
            let hasErrors = false;
            
            // Email validation
            if (!email) {
                showError('signin-email', 'Email is required');
                hasErrors = true;
            } else if (!isValidEmail(email)) {
                showError('signin-email', 'Please enter a valid email address');
                hasErrors = true;
            } else {
                // Check if email exists (simulate checking database)
                const userExists = existingUsers.find(user => user.email === email);
                if (!userExists) {
                    showError('signin-email', 'No account found with this email address');
                    hasErrors = true;
                }
            }
            
            // Password validation
            if (!password) {
                showError('signin-password', 'Password is required');
                hasErrors = true;
            } else if (password.length < 6) {
                showError('signin-password', 'Incorrect password');
                hasErrors = true;
            } else {
                // Simulate password verification (in real app, this would be done server-side)
                if (password !== 'password123') { // Simulated correct password
                    showError('signin-password', 'Incorrect password');
                    hasErrors = true;
                }
            }
            
            if (hasErrors) {
                return;
            }
            
            // Show loading state
            submitBtn.classList.add('loading');
            submitBtn.textContent = 'Signing In...';
            
            // Simulate API call delay
            setTimeout(() => {
                submitBtn.classList.remove('loading');
                submitBtn.textContent = 'Enter the Game';
                alert(`🎉 Welcome back, ${email}!\n\nRedirecting to the game...`);
            }, 1500);
        }

        function handleSignUp(event) {
            event.preventDefault();
            clearAllErrors();
            
            const username = document.getElementById('signup-username').value;
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const submitBtn = event.target.querySelector('.submit-btn');
            
            let hasErrors = false;
            
            // Username validation
            if (!username) {
                showError('signup-username', 'Username is required');
                hasErrors = true;
            } else if (username.length < 3) {
                showError('signup-username', 'Username must be at least 3 characters long');
                hasErrors = true;
            } else {
                // Check if username is taken
                const usernameTaken = existingUsers.find(user => user.username.toLowerCase() === username.toLowerCase());
                if (usernameTaken) {
                    showError('signup-username', 'This username is already taken');
                    hasErrors = true;
                }
            }
            
            // Email validation
            if (!email) {
                showError('signup-email', 'Email is required');
                hasErrors = true;
            } else if (!isValidEmail(email)) {
                showError('signup-email', 'Please enter a valid email address');
                hasErrors = true;
            } else {
                // Check if email already exists
                const emailExists = existingUsers.find(user => user.email.toLowerCase() === email.toLowerCase());
                if (emailExists) {
                    showError('signup-email', 'An account with this email already exists');
                    hasErrors = true;
                }
            }
            
            // Password validation
            if (!password) {
                showError('signup-password', 'Password is required');
                hasErrors = true;
            } else if (password.length < 6) {
                showError('signup-password', 'Password must be at least 6 characters long');
                hasErrors = true;
            } else if (!checkPasswordStrength()) {
                showError('signup-password', 'Please create a stronger password');
                hasErrors = true;
            }
            
            // Confirm password validation
            if (!confirmPassword) {
                showError('confirm-password', 'Please confirm your password');
                hasErrors = true;
            } else if (password !== confirmPassword) {
                showError('confirm-password', 'Passwords do not match');
                hasErrors = true;
            }
            
            if (hasErrors) {
                return;
            }
            
            // Show loading state
            submitBtn.classList.add('loading');
            submitBtn.textContent = 'Creating Account...';
            
            // Simulate API call delay
            setTimeout(() => {
                submitBtn.classList.remove('loading');
                submitBtn.textContent = 'Join the Adventure';
                alert(`🎉 Welcome to Riddle Quest, ${username}!\n\nYour account has been created successfully!`);
                
                // Here you would typically send the data to your server
                console.log('Sign Up Data:', { 
                    username, 
                    email, 
                    password, 
                    avatar: selectedAvatar 
                });
            }, 1500);
        }

        // Add real-time validation
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to form inputs
            const inputs = document.querySelectorAll('input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentNode.style.transform = 'translateY(-2px)';
                    // Clear error when user starts typing
                    if (this.classList.contains('input-error')) {
                        hideError(this.id);
                    }
                });
                
                input.addEventListener('blur', function() {
                    this.parentNode.style.transform = 'translateY(0)';
                });
                
                // Real-time validation for email
                if (input.type === 'email') {
                    input.addEventListener('input', function() {
                        if (this.value && !isValidEmail(this.value)) {
                            showError(this.id, 'Please enter a valid email address');
                        } else {
                            hideError(this.id);
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>