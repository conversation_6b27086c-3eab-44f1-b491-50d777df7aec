<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Riddle Challenge - Choose Your Category</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            width: 100%;
            padding: 20px;
            text-align: center;
        }

        .header {
            margin-bottom: 50px;
            animation: fadeInDown 1s ease-out;
        }

        .title {
            font-size: clamp(2.5rem, 8vw, 4rem);
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite, textGlow 2s ease-in-out infinite alternate;
            margin-bottom: 20px;
        }

        .subtitle {
            font-size: clamp(1.1rem, 3vw, 1.5rem);
            color: #b0b0b0;
            margin-bottom: 10px;
            animation: fadeIn 1.5s ease-out;
        }

        .description {
            font-size: clamp(0.9rem, 2.5vw, 1.1rem);
            color: #888;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
            animation: fadeIn 2s ease-out;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 50px;
            animation: slideUp 1s ease-out 0.5s both;
        }

        .category-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px 30px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
            transform: translateY(20px);
            opacity: 0;
            animation: cardAppear 0.8s ease-out forwards;
        }

        .category-card:nth-child(1) { animation-delay: 0.2s; }
        .category-card:nth-child(2) { animation-delay: 0.4s; }
        .category-card:nth-child(3) { animation-delay: 0.6s; }

        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.6s;
        }

        .category-card:hover::before {
            left: 100%;
        }

        .category-card:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .category-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
            transition: transform 0.3s ease;
        }

        .category-card:hover .category-icon {
            transform: rotateY(180deg) scale(1.1);
        }

        .category-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #fff;
        }

        .category-description {
            color: #ccc;
            font-size: 1rem;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .category-examples {
            color: #888;
            font-size: 0.9rem;
            font-style: italic;
        }

        .math-logic {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 107, 107, 0.05));
        }

        .math-logic:hover {
            box-shadow: 0 20px 40px rgba(255, 107, 107, 0.2);
        }

        .wordplay {
            background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), rgba(78, 205, 196, 0.05));
        }

        .wordplay:hover {
            box-shadow: 0 20px 40px rgba(78, 205, 196, 0.2);
        }

        .general {
            background: linear-gradient(135deg, rgba(150, 206, 180, 0.1), rgba(150, 206, 180, 0.05));
        }

        .general:hover {
            box-shadow: 0 20px 40px rgba(150, 206, 180, 0.2);
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 20s infinite linear;
        }

        .shape:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 60%;
            right: 10%;
            animation-delay: -7s;
        }

        .shape:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: -14s;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes textGlow {
            0% { text-shadow: 0 0 20px rgba(255, 255, 255, 0.5); }
            100% { text-shadow: 0 0 30px rgba(255, 255, 255, 0.8); }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes cardAppear {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
            100% { transform: translateY(0) rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .selected {
            animation: pulse 1s ease-in-out;
            border-color: rgba(255, 255, 255, 0.5) !important;
            background: rgba(255, 255, 255, 0.1) !important;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .categories-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                margin-top: 30px;
            }

            .category-card {
                padding: 30px 20px;
            }

            .category-icon {
                font-size: 3rem;
            }

            .category-title {
                font-size: 1.5rem;
            }

            .container {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            .header {
                margin-bottom: 30px;
            }

            .category-card {
                padding: 25px 15px;
            }

            .category-icon {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape">🧩</div>
        <div class="shape">💭</div>
        <div class="shape">🔍</div>
    </div>

    <div class="container">
        <div class="header">
            <h1 class="title">Riddle Challenge</h1>
            <p class="subtitle">Test Your Mind, Unlock Your Potential</p>
            <p class="description">Choose your preferred category and dive into a world of brain-teasing puzzles that will challenge your thinking and spark your creativity.</p>
        </div>

        <div class="categories-grid">
            <div class="category-card math-logic" data-category="math-logic">
                <span class="category-icon">🧮</span>
                <h2 class="category-title">Math & Logic</h2>
                <p class="category-description">Challenge your analytical thinking with numerical puzzles and logical reasoning problems.</p>
                <p class="category-examples">Examples: Number sequences, logical deductions, mathematical brain teasers</p>
            </div>

            <div class="category-card wordplay" data-category="wordplay">
                <span class="category-icon">📝</span>
                <h2 class="category-title">Wordplay</h2>
                <p class="category-description">Explore the beauty of language through puns, anagrams, and clever word puzzles.</p>
                <p class="category-examples">Examples: Riddles with double meanings, rhyming puzzles, word associations</p>
            </div>

            <div class="category-card general" data-category="general">
                <span class="category-icon">🎯</span>
                <h2 class="category-title">General</h2>
                <p class="category-description">Mix of creative thinking, common sense, and lateral thinking challenges.</p>
                <p class="category-examples">Examples: Classic riddles, situational puzzles, creative problem solving</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const categoryCards = document.querySelectorAll('.category-card');
            
            categoryCards.forEach(card => {
                card.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    
                    // Add selection animation
                    this.classList.add('selected');
                    
                    // Simulate category selection with visual feedback
                    setTimeout(() => {
                        // Here you would typically navigate to the riddle game
                        // For now, we'll show an alert
                        const categoryName = this.querySelector('.category-title').textContent;
                        alert(`🎉 Great choice! Loading ${categoryName} riddles...`);
                        
                        // Remove selection class after animation
                        this.classList.remove('selected');
                    }, 1000);
                });

                // Add hover sound effect simulation
                card.addEventListener('mouseenter', function() {
                    // You could add actual sound effects here
                    console.log('Hovering over:', this.getAttribute('data-category'));
                });
            });

            // Add floating animation to shapes
            const shapes = document.querySelectorAll('.shape');
            shapes.forEach((shape, index) => {
                shape.style.fontSize = Math.random() * 20 + 20 + 'px';
                shape.style.animationDuration = (Math.random() * 10 + 15) + 's';
            });

            // Add parallax effect on mouse move
            document.addEventListener('mousemove', function(e) {
                const mouseX = e.clientX / window.innerWidth;
                const mouseY = e.clientY / window.innerHeight;
                
                shapes.forEach((shape, index) => {
                    const speed = (index + 1) * 0.5;
                    const x = (mouseX - 0.5) * speed * 20;
                    const y = (mouseY - 0.5) * speed * 20;
                    
                    shape.style.transform += ` translate(${x}px, ${y}px)`;
                });
            });
        });
    </script>
</body>
</html>