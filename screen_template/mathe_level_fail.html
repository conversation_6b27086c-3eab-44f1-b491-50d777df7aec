<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Keep Trying! - Math Riddles</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            color: #fff;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated mathematical background */
        .math-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.08;
        }

        .math-symbol {
            position: absolute;
            font-size: clamp(1rem, 3vw, 2rem);
            color: #ff6b6b;
            animation: mathFloat 18s infinite linear;
            font-weight: bold;
        }

        .math-symbol:nth-child(even) {
            color: #ffc107;
            animation-direction: reverse;
        }

        .math-symbol:nth-child(3n) {
            color: #ff8c42;
            animation-duration: 22s;
        }

        .math-symbol:nth-child(4n) {
            color: #ff6b6b;
            animation-duration: 15s;
        }

        /* Main container */
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        /* Entrance Animation Overlay */
        .entrance-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 107, 107, 0.15) 0%, rgba(12, 12, 12, 0.95) 70%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 1;
            visibility: visible;
            transition: all 1s ease;
        }

        .entrance-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .entrance-content {
            text-align: center;
            animation: shakeAndFade 2.5s ease-out forwards;
        }

        .failed-text {
            font-size: clamp(2.5rem, 7vw, 5rem);
            font-weight: bold;
            color: #ff6b6b;
            margin-bottom: 20px;
            text-shadow: 0 0 30px rgba(255, 107, 107, 0.6);
            animation: failPulse 1.5s ease-in-out infinite;
        }

        .motivation-text {
            font-size: clamp(1.2rem, 3vw, 2rem);
            color: #ffc107;
            animation: motivationGlow 2s ease-in-out infinite alternate;
        }

        /* Main content */
        .main-content {
            opacity: 0;
            transform: translateY(50px);
            transition: all 1s ease;
            text-align: center;
            width: 100%;
        }

        .main-content.show {
            opacity: 1;
            transform: translateY(0);
        }

        .header-message {
            font-size: clamp(1.8rem, 4vw, 2.5rem);
            margin-bottom: 30px;
            background: linear-gradient(45deg, #ff6b6b, #ffc107);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Progress section */
        .progress-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 107, 107, 0.3);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
            animation: slideUp 1s ease-out 0.3s both;
        }

        .progress-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 107, 107, 0.1), transparent);
            animation: rotate 10s linear infinite;
        }

        .progress-section::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            background: linear-gradient(135deg, rgba(12, 12, 12, 0.9), rgba(26, 26, 46, 0.9));
            border-radius: 23px;
            z-index: -1;
        }

        .progress-content {
            position: relative;
            z-index: 1;
        }

        .current-level {
            font-size: 1.2rem;
            color: #ffc107;
            margin-bottom: 15px;
        }

        .level-display {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: bold;
            color: #ff6b6b;
            margin-bottom: 20px;
            text-shadow: 0 0 30px rgba(255, 107, 107, 0.5);
        }

        .points-needed {
            background: rgba(255, 193, 7, 0.1);
            border: 2px solid rgba(255, 193, 7, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .points-needed-title {
            color: #ffc107;
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .points-needed-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #fff;
            text-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
        }

        .progress-bar-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
            position: relative;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #ffc107);
            border-radius: 15px;
            transition: width 2s ease;
            position: relative;
            overflow: hidden;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 2s infinite;
        }

        .progress-text {
            margin-top: 10px;
            color: #ccc;
            font-size: 1rem;
        }

        /* Motivation section */
        .motivation-section {
            background: rgba(255, 193, 7, 0.05);
            border: 1px solid rgba(255, 193, 7, 0.2);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            animation: slideUp 1s ease-out 0.5s both;
        }

        .motivation-title {
            color: #ffc107;
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .motivation-text-content {
            color: #ffe066;
            line-height: 1.6;
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .difficulty-tip {
            background: rgba(69, 183, 209, 0.1);
            border: 1px solid rgba(69, 183, 209, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }

        .difficulty-tip-title {
            color: #45b7d1;
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .difficulty-tip-text {
            color: #87ceeb;
            line-height: 1.5;
        }

        /* Stats section */
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
            width: 100%;
            animation: slideUp 1s ease-out 0.7s both;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-5px);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #ffc107, #45b7d1);
        }

        .stat-icon {
            font-size: 2.2rem;
            margin-bottom: 15px;
            display: block;
        }

        .stat-card:nth-child(1) .stat-icon { color: #ff6b6b; }
        .stat-card:nth-child(2) .stat-icon { color: #4ecdc4; }
        .stat-card:nth-child(3) .stat-icon { color: #ffc107; }
        .stat-card:nth-child(4) .stat-icon { color: #45b7d1; }

        .stat-number {
            font-size: 2.2rem;
            font-weight: bold;
            color: #fff;
            display: block;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 0.95rem;
            color: #ccc;
        }

        /* Action buttons */
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
            animation: slideUp 1s ease-out 0.9s both;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 15px 30px;
            color: #fff;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            backdrop-filter: blur(10px);
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .retry-btn {
            background: linear-gradient(45deg, #ffc107, #ff8c42);
            border-color: transparent;
        }

        .retry-btn:hover {
            box-shadow: 0 10px 25px rgba(255, 193, 7, 0.4);
        }

        .menu-btn {
            border-color: rgba(69, 183, 209, 0.5);
            color: #45b7d1;
        }

        .menu-btn:hover {
            background: rgba(69, 183, 209, 0.1);
            border-color: #45b7d1;
            box-shadow: 0 10px 25px rgba(69, 183, 209, 0.3);
        }

        /* Mathematical animation elements */
        .math-equation {
            position: absolute;
            font-size: 1.3rem;
            color: rgba(255, 107, 107, 0.4);
            animation: equationFloat 4s ease-in-out infinite;
            pointer-events: none;
        }

        .math-equation:nth-child(odd) {
            animation-delay: 1.5s;
            color: rgba(255, 193, 7, 0.4);
        }

        /* Keyframe Animations */
        @keyframes mathFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.8;
            }
            90% {
                opacity: 0.8;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        @keyframes shakeAndFade {
            0% {
                opacity: 0;
                transform: scale(0.8) rotateY(180deg);
            }
            20% {
                opacity: 1;
                transform: scale(1.1) rotateY(0deg) translateX(-10px);
            }
            30% {
                transform: scale(1.1) rotateY(0deg) translateX(10px);
            }
            40% {
                transform: scale(1.1) rotateY(0deg) translateX(-5px);
            }
            50% {
                transform: scale(1.1) rotateY(0deg) translateX(5px);
            }
            60% {
                transform: scale(1.1) rotateY(0deg) translateX(0px);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotateY(0deg);
            }
        }

        @keyframes failPulse {
            0%, 100% {
                transform: scale(1);
                text-shadow: 0 0 30px rgba(255, 107, 107, 0.6);
            }
            50% {
                transform: scale(1.05);
                text-shadow: 0 0 40px rgba(255, 107, 107, 0.8);
            }
        }

        @keyframes motivationGlow {
            0% {
                text-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
            }
            100% {
                text-shadow: 0 0 30px rgba(255, 193, 7, 0.8);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes rotate {
            to { transform: rotate(360deg); }
        }

        @keyframes shimmer {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        @keyframes equationFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-25px) rotate(8deg);
            }
        }

        @keyframes motivationParticle {
            0% {
                opacity: 1;
                transform: scale(0) rotate(0deg);
            }
            50% {
                opacity: 0.8;
                transform: scale(1) rotate(180deg);
            }
            100% {
                opacity: 0;
                transform: scale(0) rotate(360deg) translateY(-50px);
            }
        }

        @keyframes buttonParticle {
            0% {
                opacity: 1;
                transform: scale(1);
            }
            100% {
                opacity: 0;
                transform: scale(0) translateY(-20px);
            }
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .progress-section, .motivation-section {
                padding: 25px 20px;
            }

            .stats-section {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .stat-card {
                padding: 20px 15px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .action-btn {
                width: 100%;
                max-width: 280px;
                justify-content: center;
            }

            .points-needed-number {
                font-size: 2rem;
            }

            .level-display {
                font-size: clamp(2.5rem, 12vw, 4rem);
            }
        }

        @media (max-width: 480px) {
            .stats-section {
                grid-template-columns: 1fr;
            }
            
            .stat-card {
                padding: 18px;
            }
        }
    </style>
</head>
<body>
    <!-- Animated mathematical background -->
    <div class="math-background" id="mathBackground"></div>

    <!-- Entrance Animation Overlay -->
    <div class="entrance-overlay" id="entranceOverlay">
        <div class="entrance-content">
            <div class="failed-text">NOT YET!</div>
            <div class="motivation-text">Keep Pushing Forward!</div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <!-- Mathematical equation animations -->
        <div class="math-equation" style="top: 15%; left: 10%;">x² + effort = success</div>
        <div class="math-equation" style="top: 25%; right: 15%;">∫ practice dx = mastery</div>
        <div class="math-equation" style="bottom: 30%; left: 20%;">lim(attempts→∞) = victory</div>
        <div class="math-equation" style="bottom: 15%; right: 10%;">persistence > difficulty</div>

        <!-- Main Content -->
        <div class="main-content" id="mainContent">
            <h1 class="header-message">Almost There!</h1>

            <!-- Progress Section -->
            <div class="progress-section">
                <div class="progress-content">
                    <div class="current-level">Current Level</div>
                    <div class="level-display" id="currentLevel">6</div>
                    
                    <div class="points-needed">
                        <div class="points-needed-title">Points Needed to Level Up</div>
                        <div class="points-needed-number" id="pointsNeeded">1,247</div>
                    </div>

                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                    </div>
                    <div class="progress-text" id="progressText">2,153 / 3,400 points (63%)</div>
                </div>
            </div>

            <!-- Motivation Section -->
            <div class="motivation-section">
                <div class="motivation-title">
                    <span>💡</span>
                    Pro Tips for Success
                </div>
                <div class="motivation-text-content">
                    Don't give up! Every mathematician faces challenges. Your logical thinking is developing with each attempt.
                </div>
                
                <div class="difficulty-tip">
                    <div class="difficulty-tip-title">🎯 Strategy Recommendation</div>
                    <div class="difficulty-tip-text">
                        Try playing on <strong>Hard</strong> or <strong>Expert</strong> difficulty for more points per question. 
                        Higher difficulty = Higher rewards = Faster level progression!
                    </div>
                </div>
            </div>

            <!-- Stats Section -->
            <div class="stats-section">
                <div class="stat-card">
                    <span class="stat-icon">🏆</span>
                    <span class="stat-number" id="totalPoints">2,153</span>
                    <span class="stat-label">Current Points</span>
                </div>
                <div class="stat-card">
                    <span class="stat-icon">✅</span>
                    <span class="stat-number" id="correctAnswers">28</span>
                    <span class="stat-label">Correct Answers</span>
                </div>
                <div class="stat-card">
                    <span class="stat-icon">❌</span>
                    <span class="stat-number" id="wrongAnswers">12</span>
                    <span class="stat-label">Wrong Answers</span>
                </div>
                <div class="stat-card">
                    <span class="stat-icon">📈</span>
                    <span class="stat-number" id="accuracy">70%</span>
                    <span class="stat-label">Accuracy Rate</span>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="action-btn retry-btn" onclick="retryLevel()">
                    <span>🔥</span>
                    Try Again
                </button>
                <button class="action-btn menu-btn" onclick="goToMenu()">
                    <span>🏠</span>
                    Main Menu
                </button>
            </div>
        </div>
    </div>

    <script>
 // Mathematical symbols for background animation
const mathSymbols = ['∑', '∫', '∂', '∆', '∇', '∞', 'π', 'θ', 'φ', 'ψ', 'Ω', 'α', 'β', 'γ', 'λ', 'μ', 'σ', '±', '≈', '≠', '≤', '≥', '∈', '∉', '⊂', '⊆', '∪', '∩', '√', '∛', '∜', '²', '³', '⁴', 'ˣ', 'ʸ', 'ᶻ', '÷', '×', '∴', '∵'];

// Level requirements (points needed for each level)
const levelRequirements = {
    1: 0,
    2: 500,
    3: 1200,
    4: 2000,
    5: 3000,
    6: 4200,
    7: 5600,
    8: 7200,
    9: 9000,
    10: 11000,
    11: 13200,
    12: 15600,
    13: 18200,
    14: 21000,
    15: 24000,
    16: 27200,
    17: 30600,
    18: 34200,
    19: 38000,
    20: 42000
};

// Initialize the page
function initializePage() {
    createMathBackground();
    showEntranceAnimation();
}

// Create floating mathematical symbols
function createMathBackground() {
    const background = document.getElementById('mathBackground');
    
    for (let i = 0; i < 30; i++) {
        const symbol = document.createElement('div');
        symbol.className = 'math-symbol';
        symbol.textContent = mathSymbols[Math.floor(Math.random() * mathSymbols.length)];
        
        // Random positioning and timing
        symbol.style.left = Math.random() * 100 + '%';
        symbol.style.animationDelay = Math.random() * 18 + 's';
        symbol.style.animationDuration = (15 + Math.random() * 10) + 's';
        
        background.appendChild(symbol);
    }
}

// Show entrance animation
function showEntranceAnimation() {
    // Hide entrance overlay after animation
    setTimeout(() => {
        document.getElementById('entranceOverlay').classList.add('hidden');
        document.getElementById('mainContent').classList.add('show');
        updateProgressInfo();
        updateStats();
        animateProgressBar();
    }, 3500);
}

// Update progress information
function updateProgressInfo() {
    const currentLevel = getCurrentLevel();
    const currentPoints = getCurrentPoints();
    const nextLevelRequirement = levelRequirements[currentLevel + 1] || 50000;
    const pointsNeeded = nextLevelRequirement - currentPoints;
    const progressPercentage = Math.floor((currentPoints / nextLevelRequirement) * 100);

    document.getElementById('currentLevel').textContent = currentLevel;
    document.getElementById('pointsNeeded').textContent = pointsNeeded.toLocaleString();
    document.getElementById('progressText').textContent = 
        `${currentPoints.toLocaleString()} / ${nextLevelRequirement.toLocaleString()} points (${progressPercentage}%)`;
    
    // Store progress percentage for animation
    window.progressPercentage = progressPercentage;
}

// Animate progress bar
function animateProgressBar() {
    setTimeout(() => {
        document.getElementById('progressBar').style.width = window.progressPercentage + '%';
    }, 1000);
}

// Update statistics
function updateStats() {
    const stats = getGameStats();
    
    document.getElementById('totalPoints').textContent = stats.currentPoints.toLocaleString();
    document.getElementById('correctAnswers').textContent = stats.correctAnswers;
    document.getElementById('wrongAnswers').textContent = stats.wrongAnswers;
    document.getElementById('accuracy').textContent = stats.accuracy + '%';
    
    // Animate numbers counting up
    animateNumbers();
}

// Animate number counting
function animateNumbers() {
    const numbers = document.querySelectorAll('.stat-number');
    numbers.forEach((num, index) => {
        setTimeout(() => {
            const text = num.textContent.replace(/,/g, '').replace('%', '');
            const finalValue = parseInt(text) || 0;
            animateCounter(num, 0, finalValue, 1500, num.textContent.includes('%'));
        }, index * 300);
    });

    // Animate points needed separately
    setTimeout(() => {
        const pointsElement = document.getElementById('pointsNeeded');
        const finalValue = parseInt(pointsElement.textContent.replace(/,/g, ''));
        animateCounter(pointsElement, finalValue + 500, finalValue, 2000, false);
    }, 800);
}

// Counter animation helper
function animateCounter(element, start, end, duration, isPercent = false) {
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easeProgress = 1 - Math.pow(1 - progress, 3); // Ease out cubic
        
        const current = Math.floor(start + (end - start) * easeProgress);
        element.textContent = isPercent ? current + '%' : current.toLocaleString();
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

// Get current level (this would come from your game state)
function getCurrentLevel() {
    // This should be retrieved from your game state/localStorage
    return 6; // Example level
}

// Get current points (this would come from your game state)
function getCurrentPoints() {
    // This should be retrieved from your game state/localStorage
    return 2153; // Example points
}

// Get game statistics (this would come from your game state)
function getGameStats() {
    // This should be retrieved from your game state/localStorage
    return {
        currentPoints: 2153,
        correctAnswers: 28,
        wrongAnswers: 12,
        accuracy: 70
    };
}

// Button actions
function retryLevel() {
    // Add loading animation
    document.body.style.opacity = '0.7';
    
    // Create motivation particles
    createMotivationParticles();
    
    setTimeout(() => {
        // Navigate back to game
        console.log('Retrying level with renewed determination...');
        // window.location.href = 'riddle-game.html?level=' + getCurrentLevel();
        alert('Redirecting to game... (This would navigate to your game page)');
        document.body.style.opacity = '1';
    }, 1000);
}

function goToMenu() {
    // Add loading animation
    document.body.style.opacity = '0.7';
    setTimeout(() => {
        // Navigate to main menu
        console.log('Going to main menu...');
        // window.location.href = 'index.html';
        alert('Redirecting to main menu... (This would navigate to your menu page)');
        document.body.style.opacity = '1';
    }, 500);
}

// Create motivation particle effects
function createMotivationParticles() {
    const container = document.querySelector('.container');
    const colors = ['#ffc107', '#ff8c42', '#45b7d1', '#4ecdc4'];
    
    for (let i = 0; i < 15; i++) {
        const particle = document.createElement('div');
        particle.style.position = 'fixed';
        particle.style.left = Math.random() * window.innerWidth + 'px';
        particle.style.top = Math.random() * window.innerHeight + 'px';
        particle.style.width = '8px';
        particle.style.height = '8px';
        particle.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
        particle.style.borderRadius = '50%';
        particle.style.pointerEvents = 'none';
        particle.style.zIndex = '1000';
        particle.style.animation = 'motivationParticle 2s ease-out forwards';
        
        container.appendChild(particle);
        
        // Remove particle after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 2000);
    }
}

// Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>