<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Math & Logic Riddles - Classic Mode</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            color: #fff;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated mathematical background */
        .math-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.05;
        }

        .math-symbol {
            position: absolute;
            font-size: clamp(1rem, 3vw, 2rem);
            color: #ff6b6b;
            animation: mathFloat 15s infinite linear;
        }

        .math-symbol:nth-child(even) {
            color: #4ecdc4;
            animation-direction: reverse;
        }

        .math-symbol:nth-child(3n) {
            color: #45b7d1;
            animation-duration: 20s;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            animation: slideInTop 1s ease-out;
        }

        .back-button {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
        }

        .title {
            font-size: clamp(2rem, 6vw, 3rem);
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite;
            margin-bottom: 10px;
        }

        .mode-badge {
            display: inline-block;
            background: rgba(78, 205, 196, 0.2);
            color: #4ecdc4;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 1rem;
            border: 1px solid rgba(78, 205, 196, 0.3);
            margin-bottom: 20px;
        }

        .stats-bar {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px 25px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #4ecdc4;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #ccc;
            margin-top: 5px;
        }

        .difficulty-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .difficulty-selector {
            display: flex;
            gap: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 25px;
            padding: 5px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .difficulty-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            background: transparent;
            color: #ccc;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .difficulty-btn.active {
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            color: #fff;
            transform: scale(1.05);
        }

        .difficulty-btn:hover:not(.active) {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .points-info {
            font-size: 0.9rem;
            color: #b0b0b0;
            text-align: center;
        }

        .riddle-container {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            animation: slideUp 0.8s ease-out;
        }

        .riddle-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.05), transparent);
            animation: rotate 8s linear infinite;
        }

        .riddle-container::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            background: linear-gradient(135deg, rgba(12, 12, 12, 0.9), rgba(26, 26, 46, 0.9));
            border-radius: 23px;
            z-index: -1;
        }

        .riddle-content {
            position: relative;
            z-index: 1;
        }

        .riddle-number {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(69, 183, 209, 0.2);
            color: #45b7d1;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .riddle-question {
            font-size: 1.4rem;
            line-height: 1.6;
            margin-bottom: 25px;
            color: #fff;
            text-align: center;
        }

        .hint-controls {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .hint-btn {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.3);
            color: #ffc107;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .hint-btn:hover {
            background: rgba(255, 193, 7, 0.3);
            transform: translateY(-2px);
        }

        .hint-section {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            animation: fadeIn 0.8s ease-out;
            display: none;
        }

        .hint-section.show {
            display: block;
        }

        .hint-title {
            color: #ffc107;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .hint-text {
            color: #ffe066;
            line-height: 1.5;
        }

        .answer-section {
            margin-top: 30px;
        }

        .multiple-choice {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .choice-btn {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px 20px;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            text-align: left;
        }

        .choice-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .choice-btn.selected {
            background: rgba(69, 183, 209, 0.2);
            border-color: #45b7d1;
            color: #45b7d1;
        }

        .text-input {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 15px 20px;
            color: #fff;
            font-size: 1.1rem;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .text-input:focus {
            outline: none;
            border-color: #45b7d1;
            background: rgba(255, 255, 255, 0.1);
        }

        .text-input::placeholder {
            color: #888;
        }

        .submit-btn {
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            border: none;
            border-radius: 25px;
            padding: 15px 40px;
            color: #fff;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 0 auto;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(69, 183, 209, 0.3);
        }

        .submit-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Animation overlays */
        .animation-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .animation-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .success-animation {
            text-align: center;
            animation: successBounce 1s ease-out;
        }

        .fail-animation {
            text-align: center;
            animation: failShake 0.8s ease-out;
        }

        .warning-animation {
            text-align: center;
            animation: warningPulse 1s ease-out;
        }

        .animation-icon {
            font-size: 5rem;
            margin-bottom: 20px;
            display: block;
        }

        .animation-text {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .animation-subtext {
            font-size: 1.2rem;
            color: #ccc;
        }

        .success-animation .animation-icon {
            color: #4ecdc4;
        }

        .success-animation .animation-text {
            color: #4ecdc4;
        }

        .fail-animation .animation-icon {
            color: #ff6b6b;
        }

        .fail-animation .animation-text {
            color: #ff6b6b;
        }

        .warning-animation .animation-icon {
            color: #ffc107;
        }

        .warning-animation .animation-text {
            color: #ffc107;
        }

        /* Custom Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modal {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            max-width: 400px;
            width: 90%;
            text-align: center;
            backdrop-filter: blur(15px);
            animation: modalSlideIn 0.4s ease-out;
        }

        .modal-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #ffc107;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #fff;
        }

        .modal-text {
            font-size: 1rem;
            color: #ccc;
            margin-bottom: 25px;
            line-height: 1.5;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .modal-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 15px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .modal-btn.primary {
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            color: #fff;
        }

        .modal-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
        }

        .modal-btn:hover {
            transform: translateY(-2px);
        }

        /* Keyframe Animations */
        @keyframes mathFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.5;
            }
            90% {
                opacity: 0.5;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes slideInTop {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes rotate {
            to { transform: rotate(360deg); }
        }

        @keyframes successBounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-30px);
            }
            60% {
                transform: translateY(-15px);
            }
        }

        @keyframes failShake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
            20%, 40%, 60%, 80% { transform: translateX(10px); }
        }

        @keyframes warningPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .back-button {
                padding: 8px 12px;
                font-size: 0.9rem;
            }

            .back-button .back-text {
                display: none;
            }

            .riddle-container {
                padding: 25px 20px;
            }

            .stats-bar {
                gap: 15px;
            }

            .stat-item {
                padding: 12px 20px;
            }

            .multiple-choice {
                grid-template-columns: 1fr;
            }

            .difficulty-controls {
                flex-direction: column;
                gap: 15px;
            }

            .modal {
                padding: 25px 20px;
            }

            .modal-buttons {
                flex-direction: column;
            }

            .modal-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="math-background" id="mathBackground"></div>

    <div class="container">
        <button class="back-button" onclick="goBack()">
            <span>←</span>
            <span class="back-text">Back to Modes</span>
        </button>

        <div class="header">
            <h1 class="title">🧮 Math & Logic Riddles</h1>
            <div class="mode-badge">🎯 Classic Mode</div>
        </div>

        <div class="stats-bar">
            <div class="stat-item">
                <span class="stat-number" id="currentRiddle">1</span>
                <div class="stat-label">Current</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="remaining">10</span>
                <div class="stat-label">Remaining</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="score">0</span>
                <div class="stat-label">Score</div>
            </div>
        </div>

        <div class="difficulty-controls">
            <div class="difficulty-selector">
                <button class="difficulty-btn active" data-difficulty="easy">Easy</button>
                <button class="difficulty-btn" data-difficulty="medium">Medium</button>
                <button class="difficulty-btn" data-difficulty="hard">Hard</button>
            </div>
        </div>

        <div class="riddle-container">
            <div class="riddle-content">
                <div class="riddle-question" id="riddleQuestion">
                    What is the next number in this sequence: 2, 4, 8, 16, ...?
                </div>

                <div class="hint-controls">
                    <button class="hint-btn" id="hintBtn" onclick="showHint()">
                        💡 <span id="hintBtnText">Show Hint</span>
                    </button>
                </div>

                <div class="hint-section" id="hintSection">
                    <div class="hint-title">
                        💡 Hint
                    </div>
                    <div class="hint-text" id="hintText">
                        Look at how each number relates to the previous one. What operation transforms 2 into 4, and 4 into 8?
                    </div>
                </div>

                <div class="answer-section">
                    <div class="multiple-choice" id="multipleChoice">
                        <button class="choice-btn" data-answer="24">A) 24</button>
                        <button class="choice-btn" data-answer="32">B) 32</button>
                        <button class="choice-btn" data-answer="20">C) 20</button>
                        <button class="choice-btn" data-answer="18">D) 18</button>
                    </div>

                    <input type="text" class="text-input" id="textInput" placeholder="Enter your answer..." style="display: none;">

                    <button class="submit-btn" id="submitBtn" onclick="submitAnswer()">Submit Answer</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Animation Overlays -->
    <div class="animation-overlay" id="successOverlay">
        <div class="success-animation">
            <span class="animation-icon">🎉</span>
            <div class="animation-text">Excellent!</div>
            <div class="animation-subtext" id="successMessage">Correct answer! +10 points</div>
        </div>
    </div>

    <div class="animation-overlay" id="failOverlay">
        <div class="fail-animation">
            <span class="animation-icon">😔</span>
            <div class="animation-text">Oops!</div>
            <div class="animation-subtext" id="failMessage">That's not quite right. The correct answer was 32.</div>
        </div>
    </div>

    <div class="animation-overlay" id="warningOverlay">
        <div class="warning-animation">
            <span class="animation-icon">⚠️</span>
            <div class="animation-text">Hold On!</div>
            <div class="animation-subtext">Please select or enter an answer before submitting!</div>
        </div>
    </div>

    <!-- Custom Modal -->
    <div class="modal-overlay" id="difficultyModal">
        <div class="modal">
            <div class="modal-icon">⚠️</div>
            <div class="modal-title">Change Difficulty?</div>
            <div class="modal-text">
                Changing difficulty will reset your progress and start the game from the beginning. Are you sure you want to continue?
            </div>
            <div class="modal-buttons">
                <button class="modal-btn primary" id="confirmChange">Yes, Change</button>
                <button class="modal-btn secondary" id="cancelChange">No, Keep Current</button>
            </div>
        </div>
    </div>

    <script>
        let currentRiddleIndex = 0;
        let score = 0;
        let currentDifficulty = 'easy';
        let selectedAnswer = null;
        let pendingDifficulty = null;
        let hintsUsed = 0;
      let maxHints = { easy: Infinity, medium: Infinity, hard: 2 };
        
        const riddles = {
            easy: [
                {
                    question: "What is the next number in this sequence: 2, 4, 8, 16, ...?",
                    hint: "Look at how each number relates to the previous one. What operation transforms 2 into 4, and 4 into 8?",
                    choices: ["24", "32", "20", "18"],
                    correct: "32",
                    explanation: "Each number is multiplied by 2. So 16 × 2 = 32."
                },
                {
                    question: "If 3 apples cost $6, how much do 7 apples cost?",
                    hint: "First find the cost of one apple, then multiply by 7.",
                    choices: ["$12", "$14", "$15", "$21"],
                    correct: "$14",
                    explanation: "Each apple costs $2, so 7 apples cost $14."
                },
                {
                    question: "What is 25% of 80?",
                    hint: "25% is the same as 1/4. What's 80 divided by 4?",
                    choices: ["15", "20", "25", "30"],
                    correct: "20",
                    explanation: "25% of 80 = 0.25 × 80 = 20."
                },
                {
                    question: "A train travels 60 miles in 2 hours. What is its speed?",
                    hint: "Speed = Distance ÷ Time. What's 60 ÷ 2?",
                    choices: ["25 mph", "30 mph", "35 mph", "40 mph"],
                    correct: "30 mph",
                    explanation: "Speed = 60 miles ÷ 2 hours = 30 mph."
                },
                {
                    question: "If you have 12 cookies and eat 1/4 of them, how many are left?",
                    hint: "Find 1/4 of 12, then subtract from the total.",
                    choices: ["6", "7", "8", "9"],
                    correct: "9",
                    explanation: "1/4 of 12 = 3. So 12 - 3 = 9 cookies left."
                }
            ],
            medium: [
                {
                    question: "A rectangle has a perimeter of 24 units. If its length is twice its width, what is the area?",
                    hint: "Set up equations: Perimeter = 2(length + width) and length = 2 × width.",
                    choices: ["24", "32", "36", "48"],
                    correct: "32",
                    explanation: "Width = 4, Length = 8, so Area = 4 × 8 = 32."
                },
                {
                    question: "If log₂(x) = 5, what is x?",
                    hint: "Remember that log₂(x) = 5 means 2 raised to what power equals x?",
                    choices: ["10", "25", "32", "50"],
                    correct: "32",
                    explanation: "log₂(x) = 5 means 2⁵ = x, so x = 32."
                },
                {
                    question: "What is the sum of interior angles in a hexagon?",
                    hint: "Use the formula: (n-2) × 180°, where n is the number of sides.",
                    choices: ["540°", "720°", "900°", "1080°"],
                    correct: "720°",
                    explanation: "Formula: (n-2) × 180°. For hexagon: (6-2) × 180° = 720°."
                },
                {
                    question: "Solve: 2x + 3 = 15",
                    hint: "Subtract 3 from both sides, then divide by 2.",
                    choices: ["4", "5", "6", "7"],
                    correct: "6",
                    explanation: "2x + 3 = 15 → 2x = 12 → x = 6."
                },
                {
                    question: "What's the area of a circle with radius 5?",
                    hint: "Area = π × r². Use π ≈ 3.14.",
                    choices: ["78.5", "84.2", "91.6", "98.1"],
                    correct: "78.5",
                    explanation: "Area = π × 5² = π × 25 ≈ 3.14 × 25 = 78.5."
                }
            ],
            hard: [
                {
                    question: "Solve for x: 2^(x+1) + 2^(x-1) = 12",
                    hint: "Factor out 2^(x-1) from the left side.",
                    correct: "3",
                    explanation: "2^(x-1)(2² + 1) = 12 → 2^(x-1) × 5 = 12 → 2^(x-1) = 2.4 → x ≈ 2.26"
                },
                {
                    question: "What is the derivative of x³ + 2x² - 5x + 3?",
                    hint: "Use the power rule: d/dx(x^n) = n×x^(n-1).",
                    correct: "3x² + 4x - 5",
                    explanation: "Using power rule: d/dx(x³) = 3x², d/dx(2x²) = 4x, d/dx(-5x) = -5, d/dx(3) = 0."
                },
                {
                    question: "Find the limit: lim(x→0) (sin(3x))/(2x)",
                    hint: "Use the fact that lim(x→0) sin(ax)/x = a.",
                    correct: "3/2",
                    explanation: "Rewrite as (3/2) × lim(x→0) sin(3x)/(3x) = (3/2) × 1 = 3/2."
                },
                {
                    question: "What is the sum of the infinite series: 1 + 1/2 + 1/4 + 1/8 + ...?",
                    hint: "This is a geometric series with first term a = 1 and ratio r = 1/2.",
                    correct: "2",
                    explanation: "Sum = a/(1-r) = 1/(1-1/2) = 1/(1/2) = 2."
                },
                {
                    question: "If matrix A = [[2,1],[3,4]], what is det(A)?",
                    hint: "For 2×2 matrix [[a,b],[c,d]], determinant = ad - bc.",
                    correct: "5",
                    explanation: "det(A) = (2×4) - (1×3) = 8 - 3 = 5."
                }
            ]
        };
        
        // Initialize the game
        function initGame() {
            createMathBackground();
            loadRiddle();
            setupEventListeners();
        }
        
        // Create animated math background
        function createMathBackground() {
            const mathSymbols = ['∑', '∫', '∆', 'π', '∞', '≈', '≤', '≥', '∴', '∵', '√', '∇', 'α', 'β', 'γ', 'θ', 'λ', 'μ', 'σ', 'φ'];
            const background = document.getElementById('mathBackground');
            
            setInterval(() => {
                const symbol = document.createElement('div');
                symbol.className = 'math-symbol';
                symbol.textContent = mathSymbols[Math.floor(Math.random() * mathSymbols.length)];
                symbol.style.left = Math.random() * 100 + '%';
                symbol.style.animationDuration = (Math.random() * 10 + 10) + 's';
                background.appendChild(symbol);
                
                setTimeout(() => {
                    if (symbol.parentNode) {
                        symbol.parentNode.removeChild(symbol);
                    }
                }, 25000);
            }, 2000);
        }
        
        // Setup event listeners
        function setupEventListeners() {
            // Difficulty buttons
            document.querySelectorAll('.difficulty-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const newDifficulty = e.target.dataset.difficulty;
                    if (newDifficulty !== currentDifficulty) {
                        // Always show warning when switching difficulty
                        pendingDifficulty = newDifficulty;
                        showModal('difficultyModal');
                    }
                });
            });
        
            // Multiple choice buttons
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('choice-btn')) {
                    selectChoice(e.target);
                }
            });
        
            // Modal buttons
            document.getElementById('confirmChange').addEventListener('click', () => {
                hideModal('difficultyModal');
                // Reset game and change difficulty
                resetGame();
                changeDifficulty(pendingDifficulty);
                pendingDifficulty = null;
            });
        
            document.getElementById('cancelChange').addEventListener('click', () => {
                hideModal('difficultyModal');
                // Keep current difficulty active in UI
                document.querySelectorAll('.difficulty-btn').forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.dataset.difficulty === currentDifficulty) {
                        btn.classList.add('active');
                    }
                });
                pendingDifficulty = null;
            });
        
            // Text input for hard difficulty
            document.getElementById('textInput').addEventListener('input', (e) => {
                selectedAnswer = e.target.value.trim();
            });
        }
        
        // Change difficulty
        function changeDifficulty(difficulty) {
            currentDifficulty = difficulty;
            hintsUsed = 0;
            
            // Update active button
            document.querySelectorAll('.difficulty-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.difficulty === difficulty) {
                    btn.classList.add('active');
                }
            });
        
            // Show/hide appropriate input method
            const multipleChoice = document.getElementById('multipleChoice');
            const textInput = document.getElementById('textInput');
            
            if (difficulty === 'hard') {
                multipleChoice.style.display = 'none';
                textInput.style.display = 'block';
            } else {
                multipleChoice.style.display = 'grid';
                textInput.style.display = 'none';
            }
            
            loadRiddle();
        }
        
        // Reset game
        function resetGame() {
            currentRiddleIndex = 0;
            score = 0;
            selectedAnswer = null;
            updateStats();
            loadRiddle();
            hintsUsed = 0;
        }
        
        // Load current riddle
        function loadRiddle() {
            const currentRiddles = riddles[currentDifficulty];
            if (currentRiddleIndex >= currentRiddles.length) {
                // Game complete
                showGameComplete();
                return;
            }
        
            const riddle = currentRiddles[currentRiddleIndex];
            
            // Update question
            document.getElementById('riddleQuestion').textContent = riddle.question;
            
            // Update hint
            document.getElementById('hintText').textContent = riddle.hint;
            document.getElementById('hintSection').classList.remove('show');
            //document.getElementById('hintBtn').style.display = 'flex';
            updateHintButton();
            
            // Setup answer options
            if (currentDifficulty === 'hard') {
                document.getElementById('textInput').value = '';
                selectedAnswer = null;
            } else {
                // Setup multiple choice
                const choices = document.querySelectorAll('.choice-btn');
                choices.forEach((btn, index) => {
                    if (riddle.choices[index]) {
                        btn.textContent = `${String.fromCharCode(65 + index)}) ${riddle.choices[index]}`;
                        btn.dataset.answer = riddle.choices[index];
                        btn.classList.remove('selected');
                        btn.style.display = 'block';
                    } else {
                        btn.style.display = 'none';
                    }
                });
                selectedAnswer = null;
            }
            
            updateStats();
        }
        
        // Select multiple choice answer
        function selectChoice(button) {
            document.querySelectorAll('.choice-btn').forEach(btn => btn.classList.remove('selected'));
            button.classList.add('selected');
            selectedAnswer = button.dataset.answer;
        }
        
        // Show hint
        function showHint() {
            if (hintsUsed >= maxHints[currentDifficulty]) {
                showNoMoreHintsMessage();
                return;
            }
            
            document.getElementById('hintSection').classList.add('show');
            document.getElementById('hintBtn').style.display = 'none';
            hintsUsed++;
            updateHintButton();
        }
        
        function updateHintButton() {
            const hintBtn = document.getElementById('hintBtn');
            const hintBtnText = document.getElementById('hintBtnText');
            const remainingHints = maxHints[currentDifficulty] - hintsUsed;
            
            if (currentDifficulty === 'hard') {
                if (remainingHints > 0) {
                    hintBtnText.textContent = `Show Hint (${remainingHints} left)`;
                    hintBtn.style.display = 'flex';
                } else {
                    hintBtn.style.display = 'none';
                }
            } else {
                hintBtnText.textContent = 'Show Hint';
                hintBtn.style.display = 'flex';
            }
        }
        
        function showNoMoreHintsMessage() {
            const overlay = document.createElement('div');
            overlay.className = 'animation-overlay show';
            overlay.innerHTML = `
                <div class="warning-animation">
                    <span class="animation-icon">🚫</span>
                    <div class="animation-text">No More Hints!</div>
                    <div class="animation-subtext">You've used all your hints for hard difficulty.</div>
                </div>
            `;
            document.body.appendChild(overlay);
            
            setTimeout(() => {
                overlay.classList.remove('show');
                setTimeout(() => document.body.removeChild(overlay), 300);
            }, 2000);
        }

        // Submit answer
        function submitAnswer() {
            if (!selectedAnswer || selectedAnswer.trim() === '') {
                showOverlay('warningOverlay');
                return;
            }
        
            const currentRiddles = riddles[currentDifficulty];
            const riddle = currentRiddles[currentRiddleIndex];
            const isCorrect = selectedAnswer.toLowerCase().trim() === riddle.correct.toLowerCase().trim();
            
            if (isCorrect) {
                const points = getPointsForDifficulty();
                score += points;
                showCorrectAnswer(points);
            } else {
                showIncorrectAnswer(riddle.correct, riddle.explanation);
            }
            
            setTimeout(() => {
                currentRiddleIndex++;
                loadRiddle();
            }, 3000);
        }
        
        // Get points based on difficulty
        function getPointsForDifficulty() {
            switch (currentDifficulty) {
                case 'easy': return 10;
                case 'medium': return 20;
                case 'hard': return 30;
                default: return 10;
            }
        }
        
        // Show correct answer animation
        function showCorrectAnswer(points) {
            document.getElementById('successMessage').textContent = `Correct answer! +${points} points`;
            showOverlay('successOverlay');
        }
        
        // Show incorrect answer animation
        function showIncorrectAnswer(correctAnswer, explanation) {
            document.getElementById('failMessage').textContent = `The correct answer was: ${correctAnswer}. ${explanation}`;
            showOverlay('failOverlay');
        }
        
        // Show overlay
        function showOverlay(overlayId) {
            document.getElementById(overlayId).classList.add('show');
            setTimeout(() => {
                document.getElementById(overlayId).classList.remove('show');
            }, 3000);
        }
        
        // Show modal
        function showModal(modalId) {
            document.getElementById(modalId).classList.add('show');
        }
        
        // Hide modal
        function hideModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }
        
        // Update stats display
        function updateStats() {
            document.getElementById('currentRiddle').textContent = currentRiddleIndex + 1;
            document.getElementById('remaining').textContent = Math.max(0, riddles[currentDifficulty].length - currentRiddleIndex);
            document.getElementById('score').textContent = score;
        }
        
        // Show game complete
        function showGameComplete() {
            alert(`Congratulations! You've completed all ${currentDifficulty} riddles with a score of ${score} points!`);
            resetGame();
        }
        
        // Go back function (would navigate to previous page)
        function goBack() {
            if (confirm('Are you sure you want to leave? Your progress will be lost.')) {
                // In a real application, this would navigate back
                console.log('Navigating back to modes selection...');
            }
        }
        
        // Initialize the game when page loads
        document.addEventListener('DOMContentLoaded', initGame);
    </script>
    </body>
    </html>