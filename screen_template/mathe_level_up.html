<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Level Up! - Math Riddles</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            color: #fff;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated mathematical background */
        .math-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.1;
        }

        .math-symbol {
            position: absolute;
            font-size: clamp(1rem, 3vw, 2rem);
            color: #ff6b6b;
            animation: mathFloat 15s infinite linear;
            font-weight: bold;
        }

        .math-symbol:nth-child(even) {
            color: #4ecdc4;
            animation-direction: reverse;
        }

        .math-symbol:nth-child(3n) {
            color: #45b7d1;
            animation-duration: 20s;
        }

        .math-symbol:nth-child(4n) {
            color: #ffc107;
            animation-duration: 12s;
        }

        /* Main container */
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        /* Entrance Animation Overlay */
        .entrance-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(78, 205, 196, 0.2) 0%, rgba(12, 12, 12, 0.95) 70%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            opacity: 1;
            visibility: visible;
            transition: all 1s ease;
        }

        .entrance-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .entrance-content {
            text-align: center;
            animation: entranceReveal 2s ease-out forwards;
        }

        .level-up-text {
            font-size: clamp(3rem, 8vw, 6rem);
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #ffc107);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 2s ease-in-out infinite;
            margin-bottom: 20px;
            text-shadow: 0 0 30px rgba(78, 205, 196, 0.5);
        }

        .level-number {
            font-size: clamp(4rem, 10vw, 8rem);
            font-weight: bold;
            color: #4ecdc4;
            text-shadow: 0 0 50px rgba(78, 205, 196, 0.8);
            animation: levelPulse 1.5s ease-in-out infinite;
        }

        /* Main content */
        .main-content {
            opacity: 0;
            transform: translateY(50px);
            transition: all 1s ease;
            text-align: center;
            width: 100%;
        }

        .main-content.show {
            opacity: 1;
            transform: translateY(0);
        }

        .congratulations {
            font-size: clamp(2rem, 5vw, 3rem);
            margin-bottom: 30px;
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Nickname reveal section */
        .nickname-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border: 2px solid rgba(78, 205, 196, 0.3);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
            animation: slideUp 1s ease-out 0.5s both;
        }

        .nickname-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(78, 205, 196, 0.1), transparent);
            animation: rotate 8s linear infinite;
        }

        .nickname-section::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            background: linear-gradient(135deg, rgba(12, 12, 12, 0.9), rgba(26, 26, 46, 0.9));
            border-radius: 23px;
            z-index: -1;
        }

        .nickname-content {
            position: relative;
            z-index: 1;
        }

        .new-title {
            font-size: 1.2rem;
            color: #ffc107;
            margin-bottom: 15px;
        }

        .nickname {
            font-size: clamp(2rem, 6vw, 3.5rem);
            font-weight: bold;
            color: #4ecdc4;
            margin-bottom: 15px;
            animation: nicknameReveal 2s ease-out;
            text-shadow: 0 0 30px rgba(78, 205, 196, 0.5);
        }

        .nickname-description {
            font-size: 1.1rem;
            color: #ccc;
            line-height: 1.5;
        }

        /* Stats section */
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
            width: 100%;
            animation: slideUp 1s ease-out 0.7s both;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-5px);
            border-color: rgba(78, 205, 196, 0.3);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .stat-card:nth-child(1) .stat-icon { color: #4ecdc4; }
        .stat-card:nth-child(2) .stat-icon { color: #45b7d1; }
        .stat-card:nth-child(3) .stat-icon { color: #ff6b6b; }
        .stat-card:nth-child(4) .stat-icon { color: #ffc107; }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #fff;
            display: block;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1rem;
            color: #ccc;
        }

        /* Action buttons */
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
            animation: slideUp 1s ease-out 0.9s both;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 15px 30px;
            color: #fff;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            backdrop-filter: blur(10px);
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .next-btn {
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            border-color: transparent;
        }

        .next-btn:hover {
            box-shadow: 0 10px 25px rgba(78, 205, 196, 0.4);
        }

        .replay-btn {
            border-color: rgba(255, 193, 7, 0.5);
            color: #ffc107;
        }

        .replay-btn:hover {
            background: rgba(255, 193, 7, 0.1);
            border-color: #ffc107;
            box-shadow: 0 10px 25px rgba(255, 193, 7, 0.3);
        }

        .menu-btn {
            border-color: rgba(255, 107, 107, 0.5);
            color: #ff6b6b;
        }

        .menu-btn:hover {
            background: rgba(255, 107, 107, 0.1);
            border-color: #ff6b6b;
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
        }

        /* Mathematical animation elements */
        .math-equation {
            position: absolute;
            font-size: 1.5rem;
            color: rgba(78, 205, 196, 0.6);
            animation: equationFloat 3s ease-in-out infinite;
            pointer-events: none;
        }

        .math-equation:nth-child(odd) {
            animation-delay: 1s;
            color: rgba(69, 183, 209, 0.6);
        }

        /* Keyframe Animations */
        @keyframes mathFloat {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes entranceReveal {
            0% {
                opacity: 0;
                transform: scale(0.5) rotateY(180deg);
            }
            50% {
                opacity: 1;
                transform: scale(1.1) rotateY(0deg);
            }
            100% {
                opacity: 1;
                transform: scale(1) rotateY(0deg);
            }
        }

        @keyframes levelPulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes rotate {
            to { transform: rotate(360deg); }
        }

        @keyframes nicknameReveal {
            0% {
                opacity: 0;
                transform: rotateX(90deg);
                filter: blur(10px);
            }
            50% {
                opacity: 0.5;
                transform: rotateX(45deg);
                filter: blur(5px);
            }
            100% {
                opacity: 1;
                transform: rotateX(0deg);
                filter: blur(0px);
            }
        }

        @keyframes equationFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(5deg);
            }
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .nickname-section {
                padding: 25px 20px;
            }

            .stats-section {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .stat-card {
                padding: 20px;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .action-btn {
                width: 100%;
                max-width: 250px;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .stats-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Animated mathematical background -->
    <div class="math-background" id="mathBackground"></div>

    <!-- Entrance Animation Overlay -->
    <div class="entrance-overlay" id="entranceOverlay">
        <div class="entrance-content">
            <div class="level-up-text">LEVEL UP!</div>
            <div class="level-number" id="levelNumber">7</div>
        </div>
    </div>

    <!-- Main Container -->
    <div class="container">
        <!-- Mathematical equation animations -->
        <div class="math-equation" style="top: 15%; left: 10%;">∑(x²+y²) = z³</div>
        <div class="math-equation" style="top: 25%; right: 15%;">∫f(x)dx = ∞</div>
        <div class="math-equation" style="bottom: 30%; left: 20%;">π²/6 = Σ(1/n²)</div>
        <div class="math-equation" style="bottom: 15%; right: 10%;">e^(iπ) + 1 = 0</div>

        <!-- Main Content -->
        <div class="main-content" id="mainContent">
            <h1 class="congratulations">Congratulations!</h1>

            <!-- Nickname Section -->
            <div class="nickname-section">
                <div class="nickname-content">
                    <div class="new-title">🎓 You've Earned a New Title!</div>
                    <div class="nickname" id="nickname">Logic Master</div>
                    <div class="nickname-description" id="nicknameDesc">
                        Your analytical mind cuts through complex problems like a mathematical blade through theoretical chaos.
                    </div>
                </div>
            </div>

            <!-- Stats Section -->
            <div class="stats-section">
                <div class="stat-card">
                    <span class="stat-icon">🏆</span>
                    <span class="stat-number" id="totalPoints">2,847</span>
                    <span class="stat-label">Total Points</span>
                </div>
                <div class="stat-card">
                    <span class="stat-icon">✅</span>
                    <span class="stat-number" id="correctAnswers">34</span>
                    <span class="stat-label">Correct Answers</span>
                </div>
                <div class="stat-card">
                    <span class="stat-icon">❌</span>
                    <span class="stat-number" id="wrongAnswers">8</span>
                    <span class="stat-label">Wrong Answers</span>
                </div>
                <div class="stat-card">
                    <span class="stat-icon">📊</span>
                    <span class="stat-number" id="accuracy">81%</span>
                    <span class="stat-label">Accuracy Rate</span>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="action-btn next-btn" onclick="nextLevel()">
                    <span>🚀</span>
                    Next Level
                </button>
                <button class="action-btn replay-btn" onclick="replayLevel()">
                    <span>🔄</span>
                    Replay Level
                </button>
                <button class="action-btn menu-btn" onclick="goToMenu()">
                    <span>🏠</span>
                    Main Menu
                </button>
            </div>
        </div>
    </div>

    <script>
        // Mathematical symbols for background animation
        const mathSymbols = ['∑', '∫', '∂', '∆', '∇', '∞', 'π', 'θ', 'φ', 'ψ', 'Ω', 'α', 'β', 'γ', 'λ', 'μ', 'σ', '±', '≈', '≠', '≤', '≥', '∈', '∉', '⊂', '⊆', '∪', '∩', '√', '∛', '∜', '²', '³', '⁴', 'ˣ', 'ʸ', 'ᶻ'];

        // Nicknames based on levels with descriptions
        const nicknames = {
            1: { name: "Number Novice", desc: "Taking your first steps into the world of mathematical mysteries." },
            2: { name: "Puzzle Apprentice", desc: "Beginning to see patterns where others see chaos." },
            3: { name: "Logic Learner", desc: "Your reasoning skills are starting to crystallize." },
            4: { name: "Problem Solver", desc: "Challenges bend to your growing analytical prowess." },
            5: { name: "Math Explorer", desc: "Venturing into uncharted territories of numerical wisdom." },
            6: { name: "Riddle Researcher", desc: "Investigating the deep structures hidden within puzzles." },
            7: { name: "Logic Master", desc: "Your analytical mind cuts through complex problems like a mathematical blade through theoretical chaos." },
            8: { name: "Equation Expert", desc: "Mathematical relationships reveal their secrets at your touch." },
            9: { name: "Algorithm Ace", desc: "You see the elegant patterns that govern computational thinking." },
            10: { name: "Theorem Theorist", desc: "Abstract concepts dance gracefully in your mathematical mind." },
            11: { name: "Proof Pioneer", desc: "Establishing new pathways through logical reasoning." },
            12: { name: "Calculus Conqueror", desc: "Infinite possibilities unfold before your analytical gaze." },
            13: { name: "Geometry Genius", desc: "Spatial relationships bow to your dimensional understanding." },
            14: { name: "Statistics Sage", desc: "Probability and data reveal their hidden truths to you." },
            15: { name: "Mathematical Maverick", desc: "You forge new paths through the landscape of pure logic." },
            16: { name: "Logic Luminary", desc: "Your reasoning illuminates the darkest corners of complex problems." },
            17: { name: "Analytical Architect", desc: "Building elegant solutions from the foundation of pure thought." },
            18: { name: "Problem Philosopher", desc: "Contemplating the deepest questions of mathematical existence." },
            19: { name: "Riddle Sage", desc: "Ancient wisdom flows through your puzzle-solving prowess." },
            20: { name: "Mathematical Mystic", desc: "You have transcended ordinary problem-solving to achieve mathematical enlightenment." }
        };

        // Initialize the page
        function initializePage() {
            createMathBackground();
            showEntranceAnimation();
        }

        // Create floating mathematical symbols
        function createMathBackground() {
            const background = document.getElementById('mathBackground');
            
            for (let i = 0; i < 25; i++) {
                const symbol = document.createElement('div');
                symbol.className = 'math-symbol';
                symbol.textContent = mathSymbols[Math.floor(Math.random() * mathSymbols.length)];
                
                // Random positioning and timing
                symbol.style.left = Math.random() * 100 + '%';
                symbol.style.animationDelay = Math.random() * 15 + 's';
                symbol.style.animationDuration = (12 + Math.random() * 8) + 's';
                
                background.appendChild(symbol);
            }
        }

        // Show entrance animation
        function showEntranceAnimation() {
            const currentLevel = getCurrentLevel();
            document.getElementById('levelNumber').textContent = currentLevel;
            
            // Hide entrance overlay after animation
            setTimeout(() => {
                document.getElementById('entranceOverlay').classList.add('hidden');
                document.getElementById('mainContent').classList.add('show');
                updateNickname(currentLevel);
                updateStats();
            }, 3000);
        }

        // Update nickname based on level
        function updateNickname(level) {
            const nicknameData = nicknames[level] || nicknames[Math.min(level, 20)];
            if (level > 20) {
                nicknameData.name = "Legendary Math God";
                nicknameData.desc = "You have ascended beyond mortal mathematical comprehension.";
            }
            
            document.getElementById('nickname').textContent = nicknameData.name;
            document.getElementById('nicknameDesc').textContent = nicknameData.desc;
        }

        // Update statistics
        function updateStats() {
            // These would typically come from your game state
            const stats = getGameStats();
            
            document.getElementById('totalPoints').textContent = stats.totalPoints.toLocaleString();
            document.getElementById('correctAnswers').textContent = stats.correctAnswers;
            document.getElementById('wrongAnswers').textContent = stats.wrongAnswers;
            document.getElementById('accuracy').textContent = stats.accuracy + '%';
            
            // Animate numbers counting up
            animateNumbers();
        }

        // Animate number counting
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');
            numbers.forEach((num, index) => {
                setTimeout(() => {
                    const text = num.textContent.replace(/,/g, '').replace('%', '');
                    const finalValue = parseInt(text) || 0;
                    animateCounter(num, 0, finalValue, 1000, num.textContent.includes('%'));
                }, index * 200);
            });
        }

        // Counter animation helper
        function animateCounter(element, start, end, duration, isPercent = false) {
            const startTime = performance.now();
            
            function update(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const easeProgress = 1 - Math.pow(1 - progress, 3); // Ease out cubic
                
                const current = Math.floor(start + (end - start) * easeProgress);
                element.textContent = isPercent ? current + '%' : current.toLocaleString();
                
                if (progress < 1) {
                    requestAnimationFrame(update);
                }
            }
            
            requestAnimationFrame(update);
        }

        // Get current level (this would come from your game state)
        function getCurrentLevel() {
            // This should be retrieved from your game state/localStorage
            return 7; // Example level
        }

        // Get game statistics (this would come from your game state)
        function getGameStats() {
            // This should be retrieved from your game state/localStorage
            return {
                totalPoints: 2847,
                correctAnswers: 34,
                wrongAnswers: 8,
                accuracy: 81
            };
        }

        // Button actions
        function nextLevel() {
            // Add loading animation
            document.body.style.opacity = '0.7';
            setTimeout(() => {
                // Navigate to next level
                console.log('Navigating to next level...');
                // window.location.href = 'riddle-game.html?level=' + (getCurrentLevel() + 1);
            }, 500);
        }

        function replayLevel() {
            // Add loading animation
            document.body.style.opacity = '0.7';
            setTimeout(() => {
                // Replay current level
                console.log('Replaying current level...');
                // window.location.href = 'riddle-game.html?level=' + getCurrentLevel();
            }, 500);
        }

        function goToMenu() {
            // Add loading animation
            document.body.style.opacity = '0.7';
            setTimeout(() => {
                // Navigate to main menu
                console.log('Going to main menu...');
                // window.location.href = 'index.html';
            }, 500);
        }

        // Add particle effects on button hover
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.action-btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', createParticleEffect);
            });
        });

        function createParticleEffect(event) {
            const button = event.target;
            const rect = button.getBoundingClientRect();
            
            for (let i = 0; i < 5; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'fixed';
                particle.style.left = (rect.left + Math.random() * rect.width) + 'px';
                particle.style.top = (rect.top + Math.random() * rect.height) + 'px';
                particle.style.width = '4px';
                particle.style.height = '4px';
                particle.style.backgroundColor = '#4ecdc4';
                particle.style.borderRadius = '50%';
                particle.style.pointerEvents = 'none';
                particle.style.zIndex = '1000';
                particle.style.animation = 'particleFloat 1s ease-out forwards';
                
                document.body.appendChild(particle);
                
                setTimeout(() => {
                    particle.remove();
                }, 1000);
            }
        }

        // Add particle animation CSS
        const particleStyle = document.createElement('style');
        particleStyle.textContent = `
            @keyframes particleFloat {
                0% {
                    opacity: 1;
                    transform: translateY(0px) scale(1);
                }
                100% {
                    opacity: 0;
                    transform: translateY(-50px) scale(0);
                }
            }
        `;
        document.head.appendChild(particleStyle);

        // Initialize the page when loaded
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>